<component name="ProjectRunConfigurationManager">
  <configuration default="false" name="AsyncTransactionListenerApplication" type="Application" factoryName="Application" nameIsGenerated="true">
    <option name="ALTERNATIVE_JRE_PATH" value="corretto-17" />
    <option name="ALTERNATIVE_JRE_PATH_ENABLED" value="true" />
    <option name="MAIN_CLASS_NAME" value="com.peoplestrust.transaction.async.listener.AsyncTransactionListenerApplication" />
    <module name="ledger-transaction-async-listener" />
    <option name="VM_PARAMETERS" value="-Dserver.port=8089" />
    <extension name="coverage">
      <pattern>
        <option name="PATTERN" value="com.peoplestrust.transaction.async.listener.*" />
        <option name="ENABLED" value="true" />
      </pattern>
    </extension>
    <extension name="software.aws.toolkits.jetbrains.core.execution.JavaAwsConnectionExtension">
      <option name="credential" />
      <option name="region" />
      <option name="useCurrentConnection" value="false" />
    </extension>
    <method v="2">
      <option name="Make" enabled="true" />
    </method>
  </configuration>
</component>