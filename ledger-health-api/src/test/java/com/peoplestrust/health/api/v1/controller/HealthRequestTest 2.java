package com.peoplestrust.health.api.v1.controller;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.peoplestrust.health.api.v1.HealthTestUtil;
import com.peoplestrust.health.api.v1.service.CloudWatchLogQuery;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

import com.peoplestrust.util.api.common.config.APICommonUtilConstant;
import lombok.extern.slf4j.Slf4j;
import org.json.JSONArray;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;

@ExtendWith(MockitoExtension.class)
@SpringBootTest
@AutoConfigureMockMvc
@Slf4j

public class HealthRequestTest {
  private final String URL = "/v1/ledger/health/cloudwatch/request";
  @Autowired
  private MockMvc mockMvc;
  @MockBean
  private CloudWatchLogQuery cloudWatchLogQuery;
  private HttpHeaders headers;
  private ObjectMapper objectMapper;

  @BeforeEach
  public void setupBeforeTest() {
    headers = new HttpHeaders();
    headers.add(APICommonUtilConstant.AUTHORIZATION, HealthTestUtil.JWT_TOKEN);
    headers.setContentType(MediaType.APPLICATION_JSON);

  }

  @Test
  public void getTop10RpsWithDateRange_Success() throws Exception {
    LocalDateTime startTime = LocalDateTime.of(2024, 4, 2, 9, 18);
    LocalDateTime endTime = LocalDateTime.of(2024, 4, 2, 11, 18);
    Map<String, String> map = new HashMap<>();
    map.put("all", "2");
    map.put("initiate", "2");
    map.put("commit", "1");
    JSONArray jsonArray = new JSONArray();
    jsonArray.put(map);

    when(cloudWatchLogQuery.executeQuery(anyString(), anyLong(), anyLong(), anyString())).thenReturn(jsonArray.toString());

    MvcResult result = mockMvc.perform(MockMvcRequestBuilders.get(URL)
            .param("start_time", startTime.toString())
            .param("end_time", endTime.toString())
            .headers(headers))
        .andExpect(status().isOk())
        .andReturn();

    objectMapper = new ObjectMapper();
    JsonNode jsonNode = objectMapper.readTree(Objects.requireNonNull(result.getResponse().getContentAsString()));
    log.info("jsonNode {}", jsonNode.toString());
    assertEquals("2", jsonNode.get(0).get("all").asText());
    assertEquals("2", jsonNode.get(0).get("initiate").asText());
    assertEquals("1", jsonNode.get(0).get("commit").asText());
  }

  @Test
  public void getTop10RpsWithDateRange_InValidDates() throws Exception {
    LocalDateTime startTime = LocalDateTime.of(2024, 3, 25, 10, 18, 0);
    LocalDateTime endTime = LocalDateTime.of(2024, 4, 29, 10, 18, 0);

    mockMvc.perform(MockMvcRequestBuilders.get(URL)
            .param("start_time", "2024-03-25T10:18:00")
            .param("end_time", "2024-04-29T10:18:00"))
        .andExpect(status().isBadRequest());
  }

  @Test
  public void getTop10RpsWithDateRange_NoTken_badRequest() throws Exception {

    LocalDateTime startTime = LocalDateTime.of(2024, 4, 2, 9, 18);
    LocalDateTime endTime = LocalDateTime.of(2024, 4, 2, 11, 18);
    Map<String, String> map = new HashMap<>();
    map.put("all", "2");
    map.put("initiate", "2");
    map.put("commit", "1");
    JSONArray jsonArray = new JSONArray();
    jsonArray.put(map);

    //Remove the token trigger 400 error
    headers.remove(APICommonUtilConstant.AUTHORIZATION);

    mockMvc.perform(MockMvcRequestBuilders.get(URL)
                    .param("start_time", startTime.toString())
                    .param("end_time", endTime.toString())
                    .headers(headers))
            .andExpect(status().isBadRequest())
            .andReturn();
  }

  @Test
  public void getTop10RpsWithDateRange_badTken_401Error() throws Exception {

    LocalDateTime startTime = LocalDateTime.of(2024, 4, 2, 9, 18);
    LocalDateTime endTime = LocalDateTime.of(2024, 4, 2, 11, 18);
    Map<String, String> map = new HashMap<>();
    map.put("all", "2");
    map.put("initiate", "2");
    map.put("commit", "1");
    JSONArray jsonArray = new JSONArray();
    jsonArray.put(map);

    //set bad token trigger 401 error
    headers.set(APICommonUtilConstant.AUTHORIZATION, HealthTestUtil.BAD_JWT_TOKEN);

    mockMvc.perform(MockMvcRequestBuilders.get(URL)
                    .param("start_time", startTime.toString())
                    .param("end_time", endTime.toString())
                    .headers(headers))
            .andExpect(status().isUnauthorized())
            .andReturn();
  }


  @Test
  public void getTop10RpsWithDateRange_StartTimeIsNull() throws Exception {
    LocalDateTime endTime = LocalDateTime.of(2024, 4, 28, 10, 18, 0);

    mockMvc.perform(MockMvcRequestBuilders.get(URL)
            .param("end_time", "2024-04-28T10:18:00"))
        .andExpect(status().isBadRequest());
  }

  @Test
  public void getTop10RpsWithDateRange_EndTimeIsNull() throws Exception {
    LocalDateTime startTime = LocalDateTime.of(2024, 4, 28, 10, 18, 0);

    mockMvc.perform(MockMvcRequestBuilders.get(URL)
            .param("start_time", "2024-04-28T10:18:00"))
        .andExpect(status().isBadRequest());
  }

  @Test
  public void getTop10RpsWithDateRange_StartTimeAndEndTimeAreNull() throws Exception {
    mockMvc.perform(MockMvcRequestBuilders.get(URL))
        .andExpect(status().isBadRequest());
  }


  @Test
  public void getTop10RpsWithDateRange_ThrowException() throws Exception {
    LocalDateTime startTime = LocalDateTime.of(2024, 4, 20, 15, 45);
    LocalDateTime endTime = LocalDateTime.of(2024, 4, 23, 15, 45);
    when(cloudWatchLogQuery.executeQuery(anyString(), anyLong(), anyLong(), anyString())).thenThrow(Exception.class);

    mockMvc.perform(MockMvcRequestBuilders.get(URL)
            .param("start_time", startTime.toString())
            .param("end_time", endTime.toString())
            .headers(headers))
        .andExpect(status().isInternalServerError());
  }
}
