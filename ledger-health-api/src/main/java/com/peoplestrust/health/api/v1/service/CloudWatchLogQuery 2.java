package com.peoplestrust.health.api.v1.service;

import com.peoplestrust.health.api.v1.config.HealthProperty;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import software.amazon.awssdk.auth.credentials.AwsBasicCredentials;
import software.amazon.awssdk.auth.credentials.StaticCredentialsProvider;
import software.amazon.awssdk.services.cloudwatchlogs.CloudWatchLogsClient;
import software.amazon.awssdk.services.cloudwatchlogs.model.StartQueryRequest;
import software.amazon.awssdk.services.cloudwatchlogs.model.GetQueryResultsRequest;
import software.amazon.awssdk.services.cloudwatchlogs.model.GetQueryResultsResponse;
import software.amazon.awssdk.services.cloudwatchlogs.model.QueryStatus;
import com.fasterxml.jackson.databind.ObjectMapper;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Service
public class CloudWatchLogQuery {

    private final CloudWatchLogsClient cloudWatchLogsClient;
    private final ObjectMapper objectMapper;

    private final HealthProperty healthProperty;

    @Autowired
    public CloudWatchLogQuery(HealthProperty healthProperty) {
        this.healthProperty = healthProperty;

        AwsBasicCredentials awsCreds = AwsBasicCredentials.create(
                this.healthProperty.getAccessKeyId(), this.healthProperty.getSecretAccessKey());

        this.cloudWatchLogsClient = CloudWatchLogsClient.builder()
                .credentialsProvider(StaticCredentialsProvider.create(awsCreds))
                .build();

        this.objectMapper = new ObjectMapper();
    }

    public String executeQuery(String logGroupName, long startTime, long endTime, String queryString) throws Exception {
        StartQueryRequest startQueryRequest = StartQueryRequest.builder()
                .logGroupName(logGroupName)
                .startTime(startTime / 1000) // Convert to seconds
                .endTime(endTime / 1000)
                .queryString(queryString)
                .build();

        String queryId = cloudWatchLogsClient.startQuery(startQueryRequest).queryId();
        log.info("Query ID: " + queryId);

        GetQueryResultsResponse queryResults;
        do {
            queryResults = cloudWatchLogsClient.getQueryResults(GetQueryResultsRequest.builder().queryId(queryId).build());
            Thread.sleep(1000); // Adjust polling as needed
            log.info("Query Status: " + queryResults.status());
        } while (queryResults.status() == QueryStatus.RUNNING || queryResults.status() == QueryStatus.SCHEDULED);

        if (queryResults.results().isEmpty()) {
            log.warn("No results found for the query.");
        }

        // Extract relevant data
        List<Map<String, String>> simplifiedResults = queryResults.results().stream()
                .map(resultFields -> resultFields.stream()
                        .collect(Collectors.toMap(field -> field.field(), field -> field.value())))
                .collect(Collectors.toList());

        log.info("find total record :{}", simplifiedResults.size());
        
        // Serialize the simplified results to JSON
        ObjectMapper objectMapper = new ObjectMapper();
        return objectMapper.writeValueAsString(simplifiedResults);
    }
}

