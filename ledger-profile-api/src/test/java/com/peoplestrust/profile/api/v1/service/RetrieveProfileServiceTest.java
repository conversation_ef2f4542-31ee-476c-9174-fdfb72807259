package com.peoplestrust.profile.api.v1.service;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

import com.peoplestrust.profile.api.v1.ProfileTestUtil;
import com.peoplestrust.profile.api.v1.mapper.ProfileMapper;
import com.peoplestrust.profile.api.v1.model.Profile;
import com.peoplestrust.profile.persistence.entity.ProfileEntity;
import com.peoplestrust.profile.persistence.repository.read.ReadProfileRepository;
import java.util.UUID;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
public class RetrieveProfileServiceTest {

  @Mock
  private ReadProfileRepository readProfileRepository;
  @InjectMocks
  ProfileServiceImpl profileService;
  @Mock
  private ProfileMapper profileMapper;
  UUID uuid = UUID.randomUUID();

  @Test
  public void getProfileTest() throws Exception {
    Profile profile = ProfileTestUtil.createProfile();
    ProfileEntity entity = ProfileTestUtil.getRespEntity(profile);
    when(readProfileRepository.findByRefId(any())).thenReturn(entity);
    when(profileMapper.fromProfileEntityToProfile(any())).thenReturn(profile);
    // when
    Profile returnProfile = profileService.retrieveProfile(uuid.toString());

    // then
    assertEquals(profile.getLegalName(), returnProfile.getLegalName());
    assertEquals(profile.getRefId(), returnProfile.getRefId());
    assertEquals(profile.getDisplayName(), returnProfile.getDisplayName());
    assertEquals(profile.getStatus(), returnProfile.getStatus());
    assertEquals(profile.getCreatedDateTime(), returnProfile.getCreatedDateTime());
    assertEquals(profile.getUpdatedDateTime(), returnProfile.getUpdatedDateTime());
  }


}
