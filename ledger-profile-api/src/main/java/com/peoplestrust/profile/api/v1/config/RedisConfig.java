package com.peoplestrust.profile.api.v1.config;

import com.peoplestrust.profile.api.v1.exception.RedisCacheErrorHandler;
import io.lettuce.core.ClientOptions;
import io.lettuce.core.SocketOptions;
import java.time.Duration;
import java.util.Arrays;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.CacheManager;
import org.springframework.cache.annotation.CachingConfigurer;
import org.springframework.cache.annotation.CachingConfigurerSupport;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.cache.interceptor.CacheErrorHandler;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.data.redis.cache.RedisCacheConfiguration;
import org.springframework.data.redis.cache.RedisCacheManager;
import org.springframework.data.redis.connection.RedisClusterConfiguration;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.connection.lettuce.LettuceClientConfiguration;
import org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.serializer.RedisSerializationContext;
import org.springframework.data.redis.serializer.RedisSerializer;
import org.springframework.data.redis.serializer.StringRedisSerializer;

@Configuration
@EnableCaching
public class RedisConfig extends CachingConfigurerSupport implements CachingConfigurer {

  @Autowired
  ProfileProperty profileProperty;

  /**
   * Defining bean for Redis Connection Factory
   *
   * @return
   */
  @Bean
  public RedisConnectionFactory redisConnectionFactory() {
    // Defining the redis cluster nodes
    List<String> nodes = Arrays.asList(profileProperty.getRedisClusterNodes().split(","));
    RedisClusterConfiguration clusterConfig = new RedisClusterConfiguration(nodes);

    LettuceClientConfiguration clientConfig = LettuceClientConfiguration.builder()
        // Defining the redis read timeout
        .commandTimeout(Duration.ofMillis(profileProperty.getRedisReadTimeout()))
        .clientOptions(ClientOptions.builder()
            .socketOptions(SocketOptions.builder()
                // Defining the redis connection timeout
                .connectTimeout(Duration.ofMillis(profileProperty.getRedisSocketTimeout()))
                .build())
            .build())
        .build();

    return new LettuceConnectionFactory(clusterConfig, clientConfig);
  }

  /**
   * Initialize the Redis connection template.
   *
   * @param redisConnectionFactory
   * @return
   */
  @Bean
  public RedisTemplate<String, Object> redisTemplate(RedisConnectionFactory redisConnectionFactory) {
    RedisTemplate<String, Object> redisTemplate = new RedisTemplate<>();
    redisTemplate.setConnectionFactory(redisConnectionFactory);
    redisTemplate.setKeySerializer(new StringRedisSerializer());
    redisTemplate.setHashKeySerializer(new StringRedisSerializer());
    redisTemplate.setEnableTransactionSupport(true);
    redisTemplate.afterPropertiesSet();

    return redisTemplate;
  }

  /**
   * Primary Cache Manager
   *
   * @param redisConnectionFactory
   * @return
   */
  @Bean("cacheManagerProfile")
  @Primary
  public CacheManager cacheManagerProfile(RedisConnectionFactory redisConnectionFactory) {
    return RedisCacheManager
        .builder(redisConnectionFactory)
        .cacheDefaults(RedisCacheConfiguration
            .defaultCacheConfig()
            .disableCachingNullValues()
            .serializeValuesWith(RedisSerializationContext.SerializationPair.fromSerializer(RedisSerializer.json())))
        .build();
  }

  /**
   * Defining Redis Error Handler
   *
   * @return
   */
  @Override
  public CacheErrorHandler errorHandler() {
    return new RedisCacheErrorHandler();
  }

}