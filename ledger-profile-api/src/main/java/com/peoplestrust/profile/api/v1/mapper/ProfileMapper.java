package com.peoplestrust.profile.api.v1.mapper;

import static com.peoplestrust.util.api.common.util.Utils.asByteArray;

import com.peoplestrust.profile.api.v1.model.Profile;
import com.peoplestrust.profile.domain.model.CreateLedgerProfileRequest;
import com.peoplestrust.profile.domain.model.UpdateLedgerProfileRequest;
import com.peoplestrust.profile.domain.model.UpdateLedgerProfileStatusRequest;
import com.peoplestrust.profile.persistence.entity.ProfileEntity;
import java.util.Base64;
import java.util.List;
import java.util.UUID;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;

/**
 * Interface for Profile mapper
 */
@Mapper(componentModel = "spring")
public interface ProfileMapper {

  /**
   * Profile -- persistence entity to DTO mapper.
   *
   * @param profileEntity persistence entity profile object
   * @return profile
   */
  @Mapping(source = "refId", target = "refId", qualifiedByName = "uuidToString")
  @Mapping(source = "cognitoClientId", target = "cognitoClientId")
  @Mapping(source = "reason", target = "reason")
  Profile fromProfileEntityToProfile(ProfileEntity profileEntity);

  /**
   * Profiles -- persistence entity to DTO mapper.
   *
   * @param profileEntity list of persistence entity profile object
   * @return List of profile
   */
  List<Profile> fromProfilesEntityToProfiles(List<ProfileEntity> profileEntity);

  /**
   * Profile -- domain request to DTO mapper.
   *
   * @param profilePostRequest domain request object
   * @return profile
   */
  @Mapping(target = "status", constant = "DISABLED")
  @Mapping(target = "refId", expression = "java( java.util.UUID.randomUUID().toString() )")
  Profile fromProfilePostRequestToProfile(CreateLedgerProfileRequest profilePostRequest);

  /**
   * Profile -- domain request to DTO mapper.
   *
   * @param profileUpdateRequest domain request object
   * @return profile
   */
  Profile fromProfileUpdateRequestToProfile(UpdateLedgerProfileRequest profileUpdateRequest);

  /**
   * Profile -- DTO to persistence entity mapper.
   *
   * @param profile dto profile object
   * @return ProfileEntity
   */
  @Mapping(source = "refId", target = "refId", qualifiedByName = "stringToUUID")
  ProfileEntity fromProfileToProfileEntity(Profile profile);

  /**
   * Profile -- domain request to DTO mapper.
   *
   * @param updateProfileStatusRequest domain request object
   * @return Profile
   */
  Profile fromProfileUpdateStatusRequestToProfile(UpdateLedgerProfileStatusRequest updateProfileStatusRequest);

  /**
   * Utility function to map string to UUID.
   *
   * @param uuid uuid
   * @return UUID
   */
  @Named(value = "stringToUUID")
  default UUID stringToUUID(String uuid) {
    return UUID.fromString(uuid);
  }

  /**
   * Utility function to map UUID to string.
   *
   * @param uuid uuid
   * @return string
   */
  @Named(value = "uuidToString")
  default String uuidToString(UUID uuid) {
    Base64.getEncoder().encode(asByteArray(uuid));
    return uuid.toString();
  }
}
