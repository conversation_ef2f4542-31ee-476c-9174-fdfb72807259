package com.peoplestrust.profile.persistence.entity;


import io.hypersistence.utils.hibernate.type.basic.PostgreSQLEnumType;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.Size;
import lombok.*;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.Type;

import java.util.UUID;

@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "profile")
public class ProfileEntity extends DomainEntityTimeStamps {

  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  @Column(name = "id", unique = true, nullable = false)
  private int id;

  @GenericGenerator(name = "uuid", strategy = "uuid2")
  @GeneratedValue(generator = "uuid")
  @Column(name = "ref_id", unique = true, nullable = false, columnDefinition = "uuid")
  private UUID refId;

  @NotEmpty
  @NotBlank
  @Column(name = "crm_id", unique = true, nullable = false)
  @Size(min = 3, max = 50)
  private String crmId;

  @Column(name = "cognito_client_id")
  @Size(max = 50)
  private String cognitoClientId;

  @NotEmpty
  @NotBlank
  @Column(name = "display_name", unique = true, nullable = false)
  @Size(min = 3, max = 50)
  private String displayName;

  @NotEmpty
  @NotBlank
  @Column(name = "legal_name", nullable = false)
  @Size(min = 3, max = 50)
  private String legalName;

  @Enumerated(EnumType.STRING)
  @Column(name="status", nullable=false, columnDefinition = "profile.profile_status_type")
  @Type(PostgreSQLEnumType.class)
  private ProfileStatus status;

  @Column(name = "reason", nullable = false)
  private String reason;
}
