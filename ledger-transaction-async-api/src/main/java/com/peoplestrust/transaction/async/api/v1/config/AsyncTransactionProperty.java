package com.peoplestrust.transaction.async.api.v1.config;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;


@Configuration
@Getter
@Setter
@NoArgsConstructor
public class AsyncTransactionProperty {


  /**
   * Application (API) name.
   */
  @Value("${app.name}")
  private String appName;

  /**
   * Application (API) name.
   */
  @Value("${app.apiv2}")
  private boolean asyncTransactionV2;

  /**
   * property to define time stamp value
   */
  @Value("${async-transaction.api.timetolive}")
  private int timeToLive;

  /**
   * property to define kafka server address
   */
  @Value("${kafka.bootstrap-servers}")
  private String kafkaBootstrapAddress;

  /**
   * property to define kafka topic name V1
   */
  @Value("${kafka.topic}")
  private String kafkaTopic;

  /**
   * property to define kafka topic name V2
   */
  @Value("${kafka.topicV2}")
  private String kafkaTopicV2;

  /**
   * property to enable or disable SSL
   */
  @Value("${kafka.ssl.enabled}")
  private boolean sslEnabled;

  /**
   * property to define sasl password
   */
  @Value("${kafka.sasl.jaas.config.password}")
  private String saslJaasConfigPassword;

  /**
   * property to define sasl username
   */
  @Value("${kafka.sasl.jaas.config.username}")
  private String saslJaasConfigUsername;

  /**
   * property to define redis cluster nodes(url)
   */
  @Value("${spring.data.redis.cluster.nodes}")
  private String redisClusterNodes;

  /**
   * property to define redis read timeout.
   * It is the time taken to get the response back from redis server
   */
  @Value("${spring.data.redis.read.timeout}")
  private int redisReadTimeout;

  /**
   * property to define redis connection timeout.
   * It is the time taken to establish the connection with the redis server
   */
  @Value("${spring.data.redis.socket.timeout}")
  private int redisSocketTimeout;

}
