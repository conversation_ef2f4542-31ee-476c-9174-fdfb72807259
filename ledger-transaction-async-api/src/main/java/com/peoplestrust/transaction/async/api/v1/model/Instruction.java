package com.peoplestrust.transaction.async.api.v1.model;

import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

@Data
@Setter
@Getter
@Builder
@ToString
@EqualsAndHashCode
@NoArgsConstructor
@AllArgsConstructor

/**
 * Instruction DTO.
 */
public class Instruction {

  private String instructionRefId;
  private AsyncPaymentRailType paymentRail;
  private AsyncInstructionStatus status;
  private List<Transaction> transactions;
}

