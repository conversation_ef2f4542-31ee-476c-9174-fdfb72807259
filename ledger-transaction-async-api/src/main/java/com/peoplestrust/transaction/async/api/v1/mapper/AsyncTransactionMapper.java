package com.peoplestrust.transaction.async.api.v1.mapper;

import static com.peoplestrust.util.api.common.util.Utils.asByteArray;

import com.peoplestrust.transaction.async.api.v1.model.AsyncPaymentRailType;
import com.peoplestrust.transaction.async.api.v1.model.Instruction;
import com.peoplestrust.transaction.async.api.v1.model.Transaction;
import java.util.Base64;
import java.util.List;
import java.util.UUID;

import com.peoplestrust.transaction.async.domain.model.InitiateLedgerTransactionAsyncRequest;
import com.peoplestrust.transaction.async.domain.model.InitiateLedgerTransactionAsyncRequestTransactionsInner;
import com.peoplestrust.transaction.async.domain.model.PaymentRail;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingConstants;
import org.mapstruct.Named;
import org.mapstruct.ValueMapping;
import org.mapstruct.ValueMappings;

/**
 * Interface for Transaction mapper
 */
@Mapper(componentModel = "spring")
public interface AsyncTransactionMapper {

  /**
   * Enum mapping from domain payment rail to payment rail dto
   *
   * @param paymentCategoryType
   * @return
   */
  @ValueMappings({
      @ValueMapping(source = "EFT", target = "EFT"),
      @ValueMapping(source = "ETRANSFER", target = "ETRANSFER"),
      @ValueMapping(source = "WIRES", target = "WIRES"),
      @ValueMapping(source = "VISA", target = "VISA"),
      @ValueMapping(source = MappingConstants.ANY_UNMAPPED, target = MappingConstants.NULL)
  })
  AsyncPaymentRailType domainToPaymentRailDto(PaymentRail paymentCategoryType);


  /**
   * Transaction -- domain request to DTO mapper.
   *
   * @param initiateLedgerTransactionAsyncRequestTransactions request domain object
   * @return
   */
  @ValueMapping(source = "PREFUND_RESERVE", target = "RESERVE")
  List<Transaction> fromInitiateLedgerTransactionAsyncRequestEntriesToTransactionList(
      List<InitiateLedgerTransactionAsyncRequestTransactionsInner> initiateLedgerTransactionAsyncRequestTransactions);


  /**
   * Instruction -- domain request to DTO mapper.
   *
   * @param transactionAsyncRequestBody
   * @return
   */
  @Mapping(source = "transactions", target = "transactions")
  Instruction fromTransactionAsyncRequestBodyToInstruction(InitiateLedgerTransactionAsyncRequest transactionAsyncRequestBody);


  /**
   * Utility function to map string to UUID.
   *
   * @param uuid uuid
   * @return
   */
  @Named(value = "stringToUUID")
  default UUID stringToUUID(String uuid) {
    return UUID.fromString(uuid);
  }

  /**
   * Utility function to map UUID to string.
   *
   * @param uuid uuid
   * @return
   */
  @Named(value = "uuidToString")
  default String uuidToString(UUID uuid) {
    Base64.getEncoder().encode(asByteArray(uuid));
    return uuid.toString();
  }

}
