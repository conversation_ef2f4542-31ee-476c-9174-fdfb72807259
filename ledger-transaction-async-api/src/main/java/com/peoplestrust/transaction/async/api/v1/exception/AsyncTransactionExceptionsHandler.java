package com.peoplestrust.transaction.async.api.v1.exception;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.exc.InvalidFormatException;
import com.fasterxml.jackson.databind.exc.UnrecognizedPropertyException;
import com.fasterxml.jackson.databind.exc.ValueInstantiationException;
import com.peoplestrust.profile.domain.model.Error;
import com.peoplestrust.util.api.common.config.ErrorProperty;
import com.peoplestrust.util.api.common.exception.*;
import com.peoplestrust.util.api.common.util.Messages;
import lombok.extern.slf4j.Slf4j;
import org.hibernate.exception.DataException;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.HttpStatusCode;
import org.springframework.http.ResponseEntity;
import org.springframework.http.converter.HttpMessageNotReadableException;
import org.springframework.transaction.UnexpectedRollbackException;
import org.springframework.web.HttpRequestMethodNotSupportedException;
import org.springframework.web.bind.MissingRequestHeaderException;
import org.springframework.web.bind.ServletRequestBindingException;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.context.request.WebRequest;
import org.springframework.web.servlet.mvc.method.annotation.ResponseEntityExceptionHandler;

@ControllerAdvice
@Slf4j
public class AsyncTransactionExceptionsHandler extends ResponseEntityExceptionHandler {

  @Override
  protected ResponseEntity<Object> handleServletRequestBindingException(ServletRequestBindingException ex, HttpHeaders headers, HttpStatusCode status,
      WebRequest request) {

    if (ex instanceof MissingRequestHeaderException) {
      MissingRequestHeaderException e = (MissingRequestHeaderException) ex;
      ErrorDetails eo = new ErrorDetails(new Error(ErrorProperty.MISSING_HEADER.name(), e.getHeaderName()));
      return new ResponseEntity<>(eo, HttpStatus.BAD_REQUEST);
    } else {
      return new ResponseEntity<>(HttpStatus.BAD_REQUEST);
    }

  }

  @Override
  protected ResponseEntity<Object> handleHttpRequestMethodNotSupported(HttpRequestMethodNotSupportedException ex, HttpHeaders headers, HttpStatusCode status,
      WebRequest request) {
    ErrorDetails eo = new ErrorDetails(new Error(ErrorProperty.INVALID_FIELD.name(), Messages.INVALID_FIELD));
    return new ResponseEntity<>(eo, HttpStatus.NOT_FOUND);
  }

  @Override
  protected ResponseEntity<Object> handleHttpMessageNotReadable(HttpMessageNotReadableException ex, HttpHeaders headers, HttpStatusCode status,
      WebRequest request) {
    if (ex.getCause() instanceof InvalidFormatException) {
      InvalidFormatException e = (InvalidFormatException) ex.getCause();
      String fieldName = e.getPath().get(e.getPath().size() - 1).getFieldName();
      ErrorDetails eo = new ErrorDetails(new Error(ErrorProperty.INVALID_FIELD.name(), fieldName));
      return new ResponseEntity<>(eo, HttpStatus.BAD_REQUEST);
    }
    IllegalArgumentException e = (IllegalArgumentException) ex.getCause().getCause();

    ErrorDetails eo = null;
    if (ex.getCause() instanceof UnrecognizedPropertyException) {
      UnrecognizedPropertyException ure = (UnrecognizedPropertyException) ex.getCause();

      eo = new ErrorDetails(new Error(ErrorProperty.INVALID_FIELD.name(), ure.getPropertyName()));
    } else if (ex.getCause() instanceof ValueInstantiationException) {
      ValueInstantiationException vie = (ValueInstantiationException) ex.getCause();

      String fieldName = vie.getPath().get(vie.getPath().size() - 1).getFieldName();
      eo = new ErrorDetails(new Error(ErrorProperty.INVALID_FIELD.name(), fieldName + ": " + e.getMessage()));
    } else {
      eo = new ErrorDetails(new Error(ErrorProperty.INVALID_FIELD.name(), e.getMessage()));
    }

    return new ResponseEntity<>(eo, HttpStatus.BAD_REQUEST);
  }

  @ExceptionHandler(Exception.class)
  public ResponseEntity<?> globalException(Exception ex, WebRequest req) throws JsonProcessingException {
    log.error("globalException", ex);
    if (ex instanceof ValidationException) {
      ValidationException e = (ValidationException) ex;
      ErrorDetails eo = new ErrorDetails(e.getError());
      return new ResponseEntity<>(eo, HttpStatus.BAD_REQUEST);
    } else if (ex instanceof IllegalArgumentException) {
      IllegalArgumentException e = (IllegalArgumentException) ex;
      String message = ex.getMessage();
      if (message.contains("enum")) {
        String[] ls = message.split("\\.");
        message = ls[ls.length - 2] + "." + ls[ls.length - 1];
      }
      ErrorDetails eo = new ErrorDetails(new Error(ErrorProperty.INVALID_FIELD.name(), message));
      return new ResponseEntity<>(eo, HttpStatus.BAD_REQUEST);
    } else if (ex instanceof InvalidFieldException) {
      InvalidFieldException e = (InvalidFieldException) ex;
      ErrorDetails eo = new ErrorDetails(new Error(ErrorProperty.INVALID_FIELD.name(), ex.getMessage()));
      return new ResponseEntity<>(eo, HttpStatus.BAD_REQUEST);
    } else if (ex instanceof InactiveStatusException) {
      InactiveStatusException e = (InactiveStatusException) ex;
      ErrorDetails eo = new ErrorDetails(new Error(ErrorProperty.INVALID_STATUS.name(), ex.getMessage()));
      return new ResponseEntity<>(eo, HttpStatus.BAD_REQUEST);
    } else if (ex instanceof InvalidStatusTransitionException) {
      InvalidStatusTransitionException e = (InvalidStatusTransitionException) ex;
      ErrorDetails eo = new ErrorDetails(new Error(ErrorProperty.INVALID_STATUS_TRANSITION.name(), ex.getMessage()));
      return new ResponseEntity<>(eo, HttpStatus.BAD_REQUEST);
    } else if (ex instanceof InvalidListSizeException) {
      InvalidListSizeException e = (InvalidListSizeException) ex;
      ErrorDetails eo = new ErrorDetails(new Error(ErrorProperty.INVALID_LIST_SIZE.name(), ex.getMessage()));
      return new ResponseEntity<>(eo, HttpStatus.BAD_REQUEST);
    } else if (ex instanceof DataException) {
      DataException e = (DataException) ex;
      ErrorDetails eo = new ErrorDetails(new Error(ErrorProperty.INVALID_FIELD.name(), ex.getMessage()));
      return new ResponseEntity<>(eo, HttpStatus.BAD_REQUEST);
    } else if (ex instanceof MandatoryFieldException) {
      MandatoryFieldException e = (MandatoryFieldException) ex;
      ErrorDetails eo = new ErrorDetails(new Error(ErrorProperty.MISSING_FIELD.name(), ex.getMessage()));
      return new ResponseEntity<>(eo, HttpStatus.BAD_REQUEST);
    } else if (ex instanceof ResourceNotFoundException) {
      ResourceNotFoundException e = (ResourceNotFoundException) ex;
      ErrorDetails eo = new ErrorDetails(new Error(ErrorProperty.RESOURCE_NOT_FOUND.name(), ex.getMessage()));
      return new ResponseEntity<>(eo, HttpStatus.NOT_FOUND);
    } else if (ex instanceof ResourceAccountNotFoundException) {
      ResourceAccountNotFoundException e = (ResourceAccountNotFoundException) ex;
      ErrorDetails eo = new ErrorDetails(new Error(ErrorProperty.RESOURCE_NOT_FOUND.name(), ex.getMessage()));
      return new ResponseEntity<>(eo, HttpStatus.NOT_FOUND);
    } else if (ex instanceof HttpClientErrorException) {
      log.error("HttpClientErrorException", ex);
      HttpClientErrorException e = (HttpClientErrorException) ex;
      ObjectMapper mapper = new ObjectMapper();
      String message = ex.getMessage();
      ErrorDetails errorDetails = mapper.readValue(message.substring(message.indexOf("{\"error\":")), ErrorDetails.class);
      int statusCode = Integer.parseInt(message.substring(0, 3));
      return new ResponseEntity<>(errorDetails, HttpStatus.valueOf(statusCode));
    } else {
      ErrorDetails eo = new ErrorDetails(new Error(ErrorProperty.UNEXPECTED_ERROR.name(), ex.getMessage()));
      return new ResponseEntity<>(eo, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }
}
