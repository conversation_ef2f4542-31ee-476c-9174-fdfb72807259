package com.peoplestrust.schedulers.exception;

import com.peoplestrust.profile.domain.model.Error;
import com.peoplestrust.util.api.common.config.ErrorProperty;
import com.peoplestrust.util.api.common.exception.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.context.request.WebRequest;
import org.springframework.web.servlet.mvc.method.annotation.ResponseEntityExceptionHandler;

@ControllerAdvice
@Slf4j
public class BalanceSnapshotExceptionHandler extends ResponseEntityExceptionHandler {

  @ExceptionHandler(Exception.class)
  public ResponseEntity<?> globalException(Exception ex, WebRequest req) {
    if (ex instanceof ValidationException) {
      log.error("ValidationException", ex);
      ValidationException e = (ValidationException) ex;
      ErrorDetails eo = new ErrorDetails(e.getError());
      return new ResponseEntity<>(eo, HttpStatus.BAD_REQUEST);
    } else if (ex instanceof ResourceNotFoundException) {
      log.error("ResourceNotFoundException", ex);
      ResourceNotFoundException e = (ResourceNotFoundException) ex;
      ErrorDetails eo = new ErrorDetails(
          new com.peoplestrust.profile.domain.model.Error(ErrorProperty.RESOURCE_NOT_FOUND.name(),
              ex.getMessage()));
      return new ResponseEntity<>(eo, HttpStatus.NOT_FOUND);
    } else if (ex instanceof UnauthorizedException) {
      log.error("UnauthorizedException", ex);
      UnauthorizedException e = (UnauthorizedException) ex;
      ErrorDetails eo = new ErrorDetails(
          new com.peoplestrust.profile.domain.model.Error(ErrorProperty.UNAUTHORIZED.name(),
              e.getMessage()));
      return new ResponseEntity<>(eo, HttpStatus.UNAUTHORIZED);
    } else {
      log.error(ErrorProperty.INTERNAL_ERROR.name(), ex);
      ErrorDetails eo = new ErrorDetails(
          new Error(ErrorProperty.INTERNAL_ERROR.name(), ex.getMessage()));
      return new ResponseEntity<>(eo, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }
}