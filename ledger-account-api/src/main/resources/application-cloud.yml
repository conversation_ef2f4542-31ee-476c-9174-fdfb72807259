spring:
  datasource:
    account-rw:
      url: jdbc:postgresql://${ACCOUNT_WRITE_DB_HOST}:${ACCOUNT_WRITE_DB_PORT}/${ACCOUNT_WRITE_DB_NAME}?currentSchema=${ACCOUNT_DB_SCHEMA}
      username: ${ACCOUNT_WRITE_DB_USERNAME}
      password: ${ACCOUNT_WRITE_DB_PASSWORD}
      maximumPoolSize: ${MAXIMUM_POOL_SIZE}
      connectionTimeout: ${DB_CONNECTION_TIME_OUT}
    account-ro:
      url: jdbc:postgresql://${ACCOUNT_READ_DB_HOST}:${ACCOUNT_READ_DB_PORT}/${ACCOUNT_READ_DB_NAME}?currentSchema=${ACCOUNT_DB_SCHEMA}
      username: ${ACCOUNT_READ_DB_USERNAME}
      password: ${ACCOUNT_READ_DB_PASSWORD}
      maximumPoolSize: ${MAXIMUM_POOL_SIZE}
      connectionTimeout: ${DB_CONNECTION_TIME_OUT}
  data:
    redis:
      database: 0
      cluster:
        nodes: ${REDIS_DB_CLUSTER}
      read:
        timeout: ${REDIS_READ_TIMEOUT}
      socket:
        timeout: ${REDIS_CONNECTION_TIMEOUT}

  main:
    allow-bean-definition-overriding: true
    disable-cognito-jwt-verification: ${DISABLE_COGNITO_JWT_VERIFICATION}
  jackson:
    default-property-inclusion: non_empty
    deserialization:
      FAIL_ON_UNKNOWN_PROPERTIES: true
  jpa:
    properties:
      hibernate:
        jdbc.lob.non_contextual_creation: true
        dialect: org.hibernate.dialect.PostgreSQLDialect
  cache:
    type: redis

account:
  api:
    validation:
      timeout:
        connection: ${ACCOUNT_VALIDATION_SERVICE_CONNECTION_TIMEOUT}
        read: ${ACCOUNT_VALIDATION_SERVICE_READ_TIMEOUT}
    timetolive: 30000
    profile:
      url: ${PROFILE_API_URL}
    transaction:
      url: ${TRANSACTIONS_API_URL}
management:
  endpoints:
    web:
      exposure:
        include: info,health,metrics
logging:
  level:
    com:
      zaxxer:
        hikari: INFO
      peoplestrust: ${LOG_LEVEL_COM_PEOPLESTRUST}
    root: WARN  # Change this to ERROR for even fewer logs
    org.hibernate.SQL: ${LOG_LEVEL_HIBERNATE}
    APIPayloadLogger: ${LOG_LEVEL_API_PAYLOAD_LOGGER}
    PerfLogger: ${LOG_LEVEL_PERF_LOGGER}
    FlowLogger: ${LOG_LEVEL_FLOW_LOGGER}
