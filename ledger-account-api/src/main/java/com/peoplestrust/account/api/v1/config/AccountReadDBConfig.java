package com.peoplestrust.account.api.v1.config;

import com.peoplestrust.account.persistence.entity.AccountEntity;
import com.peoplestrust.account.persistence.entity.OptionsEntity;
import com.zaxxer.hikari.HikariDataSource;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.autoconfigure.jdbc.DataSourceProperties;
import org.springframework.boot.autoconfigure.orm.jpa.JpaProperties;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.orm.jpa.EntityManagerFactoryBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.orm.jpa.JpaTransactionManager;
import org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.annotation.EnableTransactionManagement;

/**
 * Configuration class for setting up the read database transaction manager.
 */
@Configuration
@EnableTransactionManagement
@EnableJpaRepositories(entityManagerFactoryRef = "readEntityManagerFactory",
    transactionManagerRef = "readTransactionManager",
    basePackages = {"com.peoplestrust.account.persistence.repository.read"})
public class AccountReadDBConfig {

  private final JpaProperties jpaProperties;

  /**
   * Constructor for AccountReadDBConfig class.
   *
   * @param jpaProperties The JpaProperties used for configuring the read database.
   */
  public AccountReadDBConfig(JpaProperties jpaProperties) {
    this.jpaProperties = jpaProperties;
  }

  /**
   * Creates a DataSourceProperties bean for the read data source.
   *
   * @return The DataSourceProperties bean for the read data source.
   */
  @Bean
  @ConfigurationProperties("spring.datasource.account-ro")
  public DataSourceProperties readDataSourceProperties() {
    return new DataSourceProperties();
  }

  /**
   * Creates a DataSource bean for the read data source.
   *
   * @return The DataSource bean for the read data source.
   */
  @Bean
  @ConfigurationProperties("spring.datasource.account-ro")
  public HikariDataSource readDataSource() {
    return readDataSourceProperties().initializeDataSourceBuilder()
        .type(HikariDataSource.class).build();
  }

  /**
   * Creates a LocalContainerEntityManagerFactoryBean for the read EntityManagerFactory.
   *
   * @param builder The EntityManagerFactoryBuilder used to build the EntityManagerFactory.
   * @return The LocalContainerEntityManagerFactoryBean for the read EntityManagerFactory.
   */
  @Bean(name = "readEntityManagerFactory")
  public LocalContainerEntityManagerFactoryBean entityManagerFactory(EntityManagerFactoryBuilder builder) {
    return builder
        .dataSource(readDataSource())
        .packages(AccountEntity.class, OptionsEntity.class)
        .properties(jpaProperties.getProperties())
        .persistenceUnit("read")
        .build();
  }

  /**
   * Creates a PlatformTransactionManager for the read database transactions.
   *
   * @param entityManagerFactoryBean The LocalContainerEntityManagerFactoryBean for the read EntityManagerFactory.
   * @return The PlatformTransactionManager for the read database transactions.
   */
  @Bean(name = "readTransactionManager")
  public PlatformTransactionManager transactionManager(
      final @Qualifier("readEntityManagerFactory") LocalContainerEntityManagerFactoryBean entityManagerFactoryBean) {
    return new JpaTransactionManager(entityManagerFactoryBean.getObject());
  }
}
