package com.peoplestrust.account.api.v1.filter;

import com.peoplestrust.account.api.v1.config.AccountProperty;
import com.peoplestrust.util.api.common.config.APICommonUtilConstant;
import com.peoplestrust.util.api.common.filter.LoggingFilter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * Logging filter specific to the <em>Account API</em>
 */
@Configuration
public class AccountLoggingFilter {

    /**
     * API properties.
     */
    @Autowired
    AccountProperty properties;

    /**
     * Configure logging filter.
     *
     * @return FilterRegistrationBean
     */
    @Bean
    public FilterRegistrationBean<LoggingFilter> loggingFilter() {
        LoggingFilter loggingFilter = new LoggingFilter();

        // set application name in logger
        loggingFilter.setApplicationName(properties.getAppName());

        // initialize the filter bean
        FilterRegistrationBean<LoggingFilter> registrationBean = new FilterRegistrationBean<>();
        registrationBean.setFilter(loggingFilter);
        registrationBean.addUrlPatterns(APICommonUtilConstant.LOGGING_REQUEST_URL_PATH_FILTER);

        return registrationBean;
    }
}
