package com.peoplestrust.account.api.v1.service;

import com.peoplestrust.account.api.v1.AccountApplication;
import com.peoplestrust.account.api.v1.common.TestUtil;
import com.peoplestrust.account.api.v1.model.Account;
import com.peoplestrust.account.persistence.entity.AccountEntity;
import com.peoplestrust.account.persistence.entity.AccountStatus;
import com.peoplestrust.account.persistence.entity.OptionsEntity;
import com.peoplestrust.account.persistence.repository.read.ReadAccountRepository;
import com.peoplestrust.transaction.domain.model.BalanceSnapshotResponse;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit.jupiter.SpringExtension;

import java.math.BigDecimal;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@SpringBootTest
@ContextConfiguration(classes = AccountApplication.class)
@ExtendWith(SpringExtension.class)
@Slf4j
public class GetLatest48BalanceHistoryTestIT {

    private static String profileId = UUID.randomUUID().toString();
    @Autowired
    AccountService accountService;
    @MockBean
    ValidationService validationService;

    @MockBean
    private ReadAccountRepository readAccountRepository;

    UUID uuid = UUID.randomUUID();


    @Test
    public void getLatest48BalanceTest() throws Exception {
        Account account = TestUtil.createAccount(profileId);
        List<BalanceSnapshotResponse> balanceResp = TestUtil.get14DaysBalanceResponse(account);
        when(validationService.retrieveLatest48Balance(any(), any(), any())).thenReturn(balanceResp);

        AccountEntity accountEntity = new AccountEntity();
        accountEntity.setProfileId(UUID.fromString(profileId));
        accountEntity.setRefId(UUID.fromString(account.getRefId()));
        accountEntity.setStatus(AccountStatus.ACTIVE);

        OptionsEntity optionsEntity = new OptionsEntity();
        optionsEntity.setOverdraftAmount(new BigDecimal(100.00));
        optionsEntity.setFundHoldDays(account.getOptions().getFundHoldDays());
        accountEntity.setOptions(optionsEntity);

        when(readAccountRepository.findByRefId(any())).thenReturn(Optional.of(accountEntity));

        List<BalanceSnapshotResponse> balanceList = accountService.retrieve48Snapshots(profileId, account.getRefId()
                , uuid.toString());
        assertTrue(balanceList.size() == balanceResp.size());
    }

}
