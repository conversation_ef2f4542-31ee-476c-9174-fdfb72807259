package com.peoplestrust.account.api.v1.service;

import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

import com.peoplestrust.account.api.v1.mapper.AccountMapper;
import com.peoplestrust.account.api.v1.mapper.OptionsMapper;
import com.peoplestrust.account.api.v1.model.Account;
import com.peoplestrust.account.api.v1.model.Options;
import com.peoplestrust.account.persistence.entity.AccountEntity;
import com.peoplestrust.account.persistence.entity.AccountStatus;
import com.peoplestrust.account.persistence.entity.MonetaryUnit;
import com.peoplestrust.account.persistence.entity.OptionsEntity;
import com.peoplestrust.account.persistence.repository.read.ReadAccountRepository;
import com.peoplestrust.account.persistence.repository.write.AccountRepository;
import com.peoplestrust.transaction.domain.model.BalanceSnapshotResponse;
import com.peoplestrust.util.api.common.util.DateUtils;
import java.math.BigDecimal;
import java.time.OffsetDateTime;
import java.time.ZoneOffset;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
@Slf4j
public class GetLatest48BalanceHistoryServiceTest {

  private static String profileId = UUID.randomUUID().toString();
  @InjectMocks
  AccountServiceImpl accountService;
  @Mock
  private AccountRepository accountRepository;
  @Mock
  private ReadAccountRepository readAccountRepository;
  @Mock
  private ValidationService validationService;

  @Test
  public void getLatest48BalanceTest() throws Exception {
    Account account = createAccount();
    List<BalanceSnapshotResponse> balanceList = get14DaysBalanceResponse(account);
    AccountEntity accountEntity = getAccountData(account);
    when(readAccountRepository.findByRefId(any())).thenReturn(Optional.of(accountEntity));
    when(validationService.retrieveLatest48Balance(any(), any(), any())).thenReturn(balanceList);
    List<BalanceSnapshotResponse> balanceResponse =
        accountService.retrieve48Snapshots(profileId, account.getRefId(), UUID.randomUUID().toString());

    assertTrue(balanceResponse.size() == balanceList.size());
  }

  private List<BalanceSnapshotResponse> get14DaysBalanceResponse(Account account) {
    BalanceSnapshotResponse b1 = new BalanceSnapshotResponse();
    BigDecimal credit = BigDecimal.valueOf(200);
    BigDecimal debit = BigDecimal.valueOf(50);
    BigDecimal reserve = BigDecimal.valueOf(50);
    b1.setTotalAmountDebit(debit);
    b1.setTotalReserveAmount(reserve);
    b1.setTotalAmountCredit(credit);
    b1.setTotalAmount(credit.subtract(debit).add(reserve));
    b1.setCreatedDateTime(OffsetDateTime.now());
    b1.setEffectiveFromDateTime(DateUtils.startOfDay(1).atOffset(ZoneOffset.UTC));
    b1.setEffectiveToDateTime(DateUtils.startOfDay(0).atOffset(ZoneOffset.UTC));
    List<BalanceSnapshotResponse> balance14Days = new ArrayList<>();
    balance14Days.add(b1);

    return balance14Days;
  }

  private Account createAccount() {
    UUID uuid = UUID.randomUUID();
    Options options = createOptions();
    Account account =
        Account.builder()
            .name("CryptoFin Peer2Peer Settlement Acct")
            .description("For all peer to peer transfers")
            .monetaryUnit("CAD")
            .options(options)
            .status(AccountStatus.INACTIVE)
            .createdDateTime(DateUtils.offset())
            .updatedDateTime(DateUtils.offset())
            .refId(uuid.toString())
            .profileId(profileId)
            .build();
    return account;
  }

  private Options createOptions() {
    Options options =
        Options.builder()
            .overdraftAmount(BigDecimal.valueOf(100000))
            .fundHoldDays(5)
            .build();
    return options;
  }

  private AccountEntity getAccountData(Account account) {
    AccountEntity accountEntity = new AccountEntity();
    OptionsEntity optionsEntity = new OptionsEntity();
    optionsEntity.setFundHoldDays(account.getOptions().getFundHoldDays());
    optionsEntity.setOverdraftAmount(account.getOptions().getOverdraftAmount());
    accountEntity.setCreatedDateTime(DateUtils.offset());
    accountEntity.setUpdatedDateTime(DateUtils.offset());
    accountEntity.setName(account.getName());
    accountEntity.setOptions(optionsEntity);
    accountEntity.setRefId(UUID.fromString(account.getRefId()));
    accountEntity.setDescription(account.getDescription());
    MonetaryUnit monetaryUnit = MonetaryUnit.valueOf(account.getMonetaryUnit());
    accountEntity.setMonetaryUnit(monetaryUnit);
    accountEntity.setStatus(AccountStatus.INACTIVE);
    accountEntity.setProfileId(UUID.fromString(profileId));

    return accountEntity;
  }

  @AfterEach
  public void doCleanUpAfterTest() {
    log.trace("cleanup - start");
    accountRepository.findByProfileId(UUID.fromString(profileId)).stream().
        forEach(e -> accountRepository.delete(e));
    log.trace("clean up - end");
  }
}
