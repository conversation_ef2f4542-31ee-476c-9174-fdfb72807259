package com.peoplestrust.account.api.v1.service;

import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

import com.peoplestrust.account.api.v1.mapper.AccountMapper;
import com.peoplestrust.account.api.v1.model.Account;
import com.peoplestrust.account.api.v1.model.Options;
import com.peoplestrust.account.domain.model.RetrieveLedgerAccountBalanceResponse;
import com.peoplestrust.account.persistence.entity.AccountEntity;
import com.peoplestrust.account.persistence.entity.AccountStatus;
import com.peoplestrust.account.persistence.entity.MonetaryUnit;
import com.peoplestrust.account.persistence.entity.OptionsEntity;
import com.peoplestrust.account.persistence.repository.read.ReadAccountRepository;
import com.peoplestrust.account.persistence.repository.write.AccountRepository;
import com.peoplestrust.util.api.common.util.DateUtils;
import java.math.BigDecimal;
import java.util.Optional;
import java.util.UUID;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
@Slf4j
public class RetrieveLedgerAccountBalanceServiceTest {

  private static String profileId = UUID.randomUUID().toString();
  @InjectMocks
  AccountServiceImpl accountService;
  @Mock
  private AccountRepository accountRepository;
  @Mock
  private ReadAccountRepository readAccountRepository;
  @Mock
  private AccountMapper accountMapper;
  @Mock
  private ValidationService validationService;

  @Test
  public void balanceTest() throws Exception {
    Account account = createAccount();
    RetrieveLedgerAccountBalanceResponse response = getBalanceResponse();
    when(validationService.retrieveBalance(any(), any(), any(), any())).thenReturn(response);

    AccountEntity accountEntity = getAccountData(account);
    when(readAccountRepository.findByRefId(any())).thenReturn(Optional.of(accountEntity));
    when(validationService.isProfile(any(), any())).thenReturn(Boolean.TRUE);
    when(accountMapper.fromAccountEntityToAccount(any())).thenReturn(account);
    accountService.createAccount(account, profileId, UUID.randomUUID().toString());
    RetrieveLedgerAccountBalanceResponse getResponse = accountService.retrieveLedgerAccountBalance(
        profileId, accountEntity.getRefId().toString(), UUID.randomUUID().toString());
    assertNotNull(getResponse);
  }

  private RetrieveLedgerAccountBalanceResponse getBalanceResponse() {
    return RetrieveLedgerAccountBalanceResponse.builder().
        accountBalance(BigDecimal.valueOf(200))
        .availableBalance(BigDecimal.valueOf(1000))
        .effectiveOn(DateUtils.offsetDateTime())
        .fundHoldAmount(BigDecimal.valueOf(200))
        .prefundReserveAmount(BigDecimal.valueOf(400))
        .overdraftAmount(BigDecimal.valueOf(600)).build();
  }

  private Account createAccount() {
    UUID uuid = UUID.randomUUID();
    Options options = createOptions();
    Account account =
        Account.builder()
            .name("CryptoFin Peer2Peer Settlement Acct")
            .description("For all peer to peer transfers")
            .monetaryUnit("CAD")
            .options(options)
            .status(AccountStatus.INACTIVE)
            .createdDateTime(DateUtils.offset())
            .updatedDateTime(DateUtils.offset())
            .refId(uuid.toString())
            .build();
    return account;
  }

  private Options createOptions() {
    Options options =
        Options.builder()
            .overdraftAmount(BigDecimal.valueOf(100000))
            .fundHoldDays(5)
            .build();
    return options;
  }

  private AccountEntity getAccountData(Account account) {
    AccountEntity accountEntity = new AccountEntity();
    OptionsEntity optionsEntity = new OptionsEntity();
    optionsEntity.setFundHoldDays(account.getOptions().getFundHoldDays());
    optionsEntity.setOverdraftAmount(account.getOptions().getOverdraftAmount());
    accountEntity.setCreatedDateTime(DateUtils.offset());
    accountEntity.setUpdatedDateTime(DateUtils.offset());
    accountEntity.setName(account.getName());
    accountEntity.setOptions(optionsEntity);
    accountEntity.setRefId(UUID.fromString(account.getRefId()));
    accountEntity.setDescription(account.getDescription());
    MonetaryUnit monetaryUnit = MonetaryUnit.valueOf(account.getMonetaryUnit());
    accountEntity.setMonetaryUnit(monetaryUnit);
    accountEntity.setStatus(AccountStatus.INACTIVE);
    accountEntity.setProfileId(UUID.fromString(profileId));
    return accountEntity;
  }

  @AfterEach
  public void doCleanUpAfterTest() {
    log.trace("cleanup - start");
    accountRepository.findByProfileId(UUID.fromString(profileId)).stream().
        forEach(e -> accountRepository.delete(e));
    log.trace("clean up - end");
  }
}
