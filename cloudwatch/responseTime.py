# responseTime.py
from cloudWatchQueryHandler import CloudWatchQueryHandler
from datetime import datetime, timedelta

# Example usage
handler = CloudWatchQueryHandler(aws_profile='PROD_LEDGER')

# Define the log group and query string
log_group = '/production/prod/general-ledger/eks/gl-eks-main-prod/application'
query_string = '''
fields @timestamp, log
| filter kubernetes.labels.app="transaction-v1"
| filter kubernetes.namespace_name="pg-ledger-prod"
| parse log "* * * [*] [*]-[*] * - *" as pg_date, pg_time, pg_log_level, pg_sa_id, pg_interaction_id, pg_thread_id, pg_class, pg_message
| filter pg_class="PerfLogger"
| parse pg_message "* | * | * | * ms" as perf_method, perf_start_date, perf_end_date, perf_elapsed
| filter perf_method like 'TransactionController'
| stats average(perf_elapsed) as average_response_time, min(perf_elapsed) as minimum_response_time, max(perf_elapsed) as maximum_response_time by bin(60s)
| sort maximum_response_time desc
| limit 10000
'''

# Specify the time range for the query
end_time = datetime.now()
start_time = end_time - timedelta(days=1)

# Convert start and end times to epoch milliseconds
start_epoch_time = int(start_time.timestamp()) * 1000
end_epoch_time = int(end_time.timestamp()) * 1000

start_us_east = handler.convert_epoch_to_timezone(start_epoch_time)
end_us_east = handler.convert_epoch_to_timezone(end_epoch_time)

print("start time="+ start_us_east.strftime('%Y-%m-%d %H:%M:%S') + " end time=" + end_us_east.strftime('%Y-%m-%d %H:%M:%S'))

# Run the query
results = handler.run_query(log_group, start_epoch_time, end_epoch_time, query_string)

# Process and save the results in JSON format
json_filename = 'gl_response_time.json'
handler.process_and_save_results(results, json_filename)

# Output the file name for confirmation
print(f"Results saved to {json_filename}")
