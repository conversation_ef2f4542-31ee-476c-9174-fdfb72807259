package com.peoplestrust.util.api.common.util;

import com.peoplestrust.util.api.common.config.APICommonUtilConstant;
import java.time.LocalDateTime;
import java.time.OffsetDateTime;
import java.time.ZoneId;
import java.time.ZoneOffset;
import java.time.ZonedDateTime;
import java.time.temporal.ChronoUnit;

public class DateUtils {

  /**
   * Retrieve UTC date as LocalDateTime.
   *
   * @return
   */
  public static LocalDateTime offset() {
    return LocalDateTime.now(ZoneOffset.UTC).truncatedTo(ChronoUnit.MILLIS);
  }

  public static Integer daysPast(OffsetDateTime date){
    return Math.toIntExact(ChronoUnit.DAYS.between(date, OffsetDateTime.now()));
  }

  public static OffsetDateTime toOffsetDateTime(String date ){
    return OffsetDateTime.parse(date);
  }
  /**
   * Retrieve UTC date as OffsetDateTime.
   *
   * @return
   */
  public static OffsetDateTime offsetDateTime() {
    return offset().atOffset(ZoneOffset.UTC);
  }

  /**
   * Retrieve start of (N) day as localDateTime .
   *
   * @param day number of days back to go (eg. 0 is today, 1 is yesterday, 2 is day before yesterday, etc.)
   * @return
   */
  public static LocalDateTime startOfDay(int day) {
    ZonedDateTime time = LocalDateTime.now().atZone(ZoneId.of(APICommonUtilConstant.AMERICA_TORONTO));
    return time.minusDays(day).withHour(0).withMinute(0).withMinute(0).withSecond(0).withNano(0).toLocalDateTime();
  }

  /**
   * Utility function to convert local date time to Offset date time
   *
   * @param localDateTime
   * @return
   */
  public static OffsetDateTime toOffsetDateTime(LocalDateTime localDateTime) {
    if (localDateTime == null) {
      return null;
    } else {
      return localDateTime.atOffset(ZoneOffset.UTC);
    }
  }

  public static LocalDateTime toLocalDateTime(OffsetDateTime offsetDateTime) {
    if (offsetDateTime == null) {
      return null;
    }
    return offsetDateTime.toLocalDateTime();
  }
}

