package com.peoplestrust.transaction.api.v1.service;

import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

import com.peoplestrust.scheduler.persistence.entity.BalanceEntity;
import com.peoplestrust.scheduler.persistence.repository.read.ReadBalanceRepository;
import com.peoplestrust.transaction.api.v1.TransactionApplication;
import com.peoplestrust.transaction.api.v1.common.TestUtil;
import com.peoplestrust.transaction.api.v1.model.Balance;
import java.util.List;
import java.util.UUID;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit.jupiter.SpringExtension;

@SpringBootTest
@ContextConfiguration(classes = TransactionApplication.class)
@ExtendWith(SpringExtension.class)
@Slf4j
public class RetrieveLatest48BalanceHistoryServiceTestIT {


  @Autowired
  private TransactionServiceImpl transactionService;

  @MockBean
  ReadBalanceRepository readBalanceRepository;

  String accountId = UUID.randomUUID().toString();
  String interactionId = UUID.randomUUID().toString();
  private static String profileId = UUID.randomUUID().toString();

  @Test
  public void get48BalanceSnapShotTest() throws Exception {

    List<BalanceEntity> balanceEntity = TestUtil.getBalanceEntityLst(profileId,accountId);
    when(readBalanceRepository.findTop48ByAccountRefIdAndProfileRefIdOrderByCreatedDateTimeDesc(any(), any())).thenReturn(balanceEntity);
    List<Balance> balance = transactionService.getLatest48BalanceHistory(profileId, accountId, interactionId);
    assertTrue(balance.size() == balanceEntity.size());
  }
}
