package com.peoplestrust.transaction.api.v1.config;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

@Configuration
@Getter
@Setter
@NoArgsConstructor
public class TransactionProperty {

  /**
   * Application (API) name.
   */
  @Value("${app.name}")
  private String appName;

  /**
   * property to define time stamp value
   */
  @Value("${transaction.api.timetolive}")
  private int timeToLive;

  /**
   * property to define profile api url to check whether profile is available/valid
   */
  @Value("${transaction.api.profile.url}")
  private String profileInternalUrl;

  /**
   * property to define account api url to get the fund hold days for a specific account
   */
  @Value("${transaction.api.account.url}")
  private String accountInternalUrl;

  /**
   * property to define account api url to validate the account and profile
   */
  @Value("${transaction.api.account.validate.url}")
  private String validateAccountInternalUrl;

  /**
   * Use which way calculation sum of amount.
   */
  @Value("${app.usespforsum:false}")
  private Boolean useStoreProcedureCalculateSum;

  /**
   *  Property to define connection timeout for validation service
   */
  @Value(value = "${transaction.api.validation.timeout.connection}")
  private int validationServiceConnectionTimeout;

  /**
   * Property to define read timeout for validation service
   */
  @Value(value = "${transaction.api.validation.timeout.read}")
  private int validationServiceReadTimeout;
}
