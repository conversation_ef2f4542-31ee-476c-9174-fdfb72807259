package com.peoplestrust.transaction.api.v1.config;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.client.SimpleClientHttpRequestFactory;
import org.springframework.web.client.RestTemplate;

@Configuration
public class RestTemplateConfig {

  @Autowired
  TransactionProperty transactionProperty;

  @Bean
  public RestTemplate getRestTemplate() {
    SimpleClientHttpRequestFactory factory = new SimpleClientHttpRequestFactory();
    // Set the connection timeout
    factory.setConnectTimeout(transactionProperty.getValidationServiceConnectionTimeout());
    // Set the read timeout
    factory.setReadTimeout(transactionProperty.getValidationServiceReadTimeout());
    return new RestTemplate(factory);
  }
}
