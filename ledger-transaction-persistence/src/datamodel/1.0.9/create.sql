-- ###############
-- # ENVIRONMENT #
-- ###############
-- # Based on env, set search path accordingly (uncomment one of the below)

-- # LOCAL
-- CREATE SCHEMA IF NOT EXISTS ledger_transaction
-- SET search_path to ledger_transaction;

-- # DEV 
-- SET search_path to transaction_dev;

-- # QAS
--  SET search_path to transaction_qa;

-- #####
-- TYPES
-- #####
DO $$ BEGIN
  -- Creating ENUM for instruction::status
CREATE TYPE instruction_status_type AS ENUM (
  'INIT_PENDING',
  'PENDING',
  'FAILED',
  'ROLLBACKED',
  'ROLL<PERSON>CKED_SYSTEM',
  'POSTED');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
  -- Creating ENUM for transaction::Status
CREATE TYPE transaction_status_type AS ENUM (
  'INIT_PENDING',
  'PENDING',
  'FAILED',
  'ROLLBACKED',
  '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>_SYSTEM',
  'POSTED',
  'REVERSED');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

-- Creating ENUM for transaction::holdtype
DO $$ BEGIN
CREATE TYPE transaction_hold_type AS ENUM (
  'HOLD',
  'INSTANT');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;


-- ######
-- TABLES
-- ######
  -- Creating INSTRUCTIONS table
CREATE TABLE IF NOT exists instructions (
  id                          INTEGER PRIMARY KEY GENERATED ALWAYS AS IDENTITY,
  profile_ref_id              UUID NOT NULL,
  account_ref_id              UUID NOT NULL,
  instruction_ref_id          VARCHAR(36) UNIQUE NOT NULL,
  payment_rail                VARCHAR NOT NULL,
  status                      instruction_status_type NOT NULL,
  created_date_time           TIMESTAMP NOT NULL,
  updated_date_time           TIMESTAMP NULL,

  CONSTRAINT uq_instruction_ref_id UNIQUE(profile_ref_id, account_ref_id, instruction_ref_id)
);

-- INDEXES for INSTRUCTIONS :: General retrieval
CREATE INDEX IF NOT exists ix_instructions_profile_account_instruction_ref_id ON instructions USING btree (profile_ref_id, account_ref_id, instruction_ref_id);
CREATE INDEX concurrently IF NOT exists ix_instructions_status_created_date_time ON instructions USING btree (status, created_date_time);
CREATE INDEX CONCURRENTLY idx_instructions_rail_partial_internal ON instructions(payment_rail) WHERE payment_rail = 'INTERNAL';

  -- Creating TRANSACTIONS table
CREATE TABLE IF NOT exists transactions (
  id                          INTEGER PRIMARY KEY GENERATED ALWAYS AS IDENTITY,
  related_id                  INTEGER NULL,
  network_payment_ref_id      VARCHAR NULL,
  profile_ref_id              UUID NOT NULL,
  account_ref_id              UUID NOT NULL,
  instruction_id              INTEGER NOT NULL,
  transaction_ref_id          VARCHAR(36) NOT NULL,
  transaction_hold            transaction_hold_type NULL,
  payment_category            VARCHAR NOT NULL,
  transaction_flow            VARCHAR NULL,
  status                      transaction_status_type NOT NULL,
  amount                      NUMERIC(13, 2) NOT NULL,
  monetary_unit               VARCHAR(3) NOT NULL,
  acceptance_date_time        TIMESTAMP NOT NULL,
  due_date_time               TIMESTAMP NULL,
  effective_date_time         TIMESTAMP NOT NULL,
  created_date_time           TIMESTAMP NOT NULL,
  updated_date_time           TIMESTAMP NULL,
  CONSTRAINT uq_transaction_ref_iud UNIQUE(profile_ref_id, account_ref_id, transaction_ref_id),
  CONSTRAINT fk_transaction_transaction FOREIGN KEY (related_id) REFERENCES transactions(id),
  CONSTRAINT fk_instruction_transaction FOREIGN KEY (instruction_id) REFERENCES instructions(id)
);

-- INDEXES for TRANSACTIONS :: Effective date based search
CREATE INDEX IF NOT exists ix_transaction_effective_date_time ON transactions USING btree (effective_date_time);

-- INDEXES for TRANSACTIONS :: General retrieval
CREATE INDEX IF NOT exists ix_transaction_profile_account_instruction ON transactions USING btree (profile_ref_id, account_ref_id, instruction_id);
CREATE INDEX IF NOT exists ix_transaction_profile_account_instruction_transaction ON transactions USING btree (profile_ref_id, account_ref_id, instruction_id, transaction_ref_id);
CREATE INDEX IF NOT exists ix_transaction_related_id ON transactions USING btree (related_id);

create index concurrently if not exists ix_transactions_instruction_id on transactions USING btree(instruction_id);

--Creating BALANCE table
CREATE TABLE IF NOT exists balance (
  id                          INTEGER PRIMARY KEY GENERATED ALWAYS AS IDENTITY,
  profile_ref_id              UUID NOT NULL,
  account_ref_id              UUID NOT NULL,
  total_amount_credit         NUMERIC(13, 2) NOT NULL,
  total_amount_debit          NUMERIC(13, 2) NOT NULL,
  total_amount                NUMERIC(13, 2) NOT NULL,
  total_reserve_amount        NUMERIC(13, 2) NULL,
  monetary_unit               VARCHAR(3) NOT NULL,
  effective_from_date_time    TIMESTAMP NOT NULL,
  effective_to_date_time      TIMESTAMP NOT NULL,
  created_date_time           TIMESTAMP NOT NULL
);

-- INDEXES for BALANCE :: General retrieval
CREATE INDEX concurrently IF NOT exists ix_balance_profile_account ON balance USING btree (profile_ref_id, account_ref_id);

-- create new store procedure to sum
CREATE OR REPLACE FUNCTION sumAmountByDateRangeInstructionId(
    p_accountRefId UUID,
    p_profileRefId UUID,
    p_effectiveDateTimeStart TIMESTAMP,
    p_effectiveDateTimeUntil TIMESTAMP,
    p_instructionId INTEGER
) RETURNS NUMERIC AS $$
DECLARE
v_sum NUMERIC;
BEGIN
SELECT sum(amount) INTO v_sum
FROM transactions
WHERE
        account_ref_id = p_accountRefId AND
        profile_ref_id = p_profileRefId AND
        status IN ('POSTED','PENDING','INIT_PENDING','REVERSED') AND
    (effective_date_time BETWEEN p_effectiveDateTimeStart AND p_effectiveDateTimeUntil) AND
        instruction_id <= p_instructionId;
RETURN v_sum;
END; $$ LANGUAGE plpgsql parallel safe;

-- create extra index
CREATE INDEX idx_transactions_status ON transactions(status)
    WHERE status IN ('POSTED','PENDING','INIT_PENDING','REVERSED');

CREATE table IF NOT EXISTS transactions_metadata (
     id                              INTEGER PRIMARY KEY GENERATED ALWAYS AS IDENTITY, -- internal unique identifier for referential integrity
     transaction_id                  INTEGER NOT NULL REFERENCES transactions(id), -- FK to transactions.ID
     meta_data_json                  JSONB,
     created_date_time               TIMESTAMP NOT NULL,
     updated_date_time               TIMESTAMP NULL
);

CREATE INDEX concurrently idx_transactions_acceptance_date_time ON transactions( acceptance_date_time DESC);

ALTER USER transaction_user SET search_path TO transaction, public;
