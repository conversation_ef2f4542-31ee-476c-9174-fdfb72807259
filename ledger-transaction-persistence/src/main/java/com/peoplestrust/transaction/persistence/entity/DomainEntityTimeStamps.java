package com.peoplestrust.transaction.persistence.entity;

import com.peoplestrust.transaction.persistence.utils.DateTimeGenerator;
import java.time.LocalDateTime;

import jakarta.persistence.Column;
import jakarta.persistence.MappedSuperclass;
import jakarta.persistence.PrePersist;
import jakarta.persistence.PreUpdate;
import lombok.Data;

@MappedSuperclass
@Data
public class DomainEntityTimeStamps {

  @Column(name = "created_date_time", nullable = false, length = 29)
  private LocalDateTime createdDateTime;

  @Column(name = "updated_date_time", length = 29)
  private LocalDateTime updatedDateTime;

  /**
   * Initialize created date time if not yet set.
   */
  @PrePersist

  protected void onCreate() {
    LocalDateTime dateTime = DateTimeGenerator.getCurrentTime();
    if (this.createdDateTime == null) {
      this.createdDateTime = dateTime;
    }
    if (this.updatedDateTime == null) {
      this.updatedDateTime = dateTime;
    }
  }
  /**
   * Updates updated date time on each change.
   */
  @PreUpdate
  protected void onUpdate() {
    this.updatedDateTime = DateTimeGenerator.getCurrentTime();
  }
}
