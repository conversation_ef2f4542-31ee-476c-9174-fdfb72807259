package com.peoplestrust.transaction.persistence.repository.write;

import com.peoplestrust.transaction.persistence.entity.TransactionEntity;
import com.peoplestrust.transaction.persistence.entity.TransactionMetadataEntity;
import java.util.Optional;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

@Repository
public interface TransactionMetadataRepository extends JpaRepository<TransactionMetadataEntity, Integer> {
  Optional<TransactionMetadataEntity> findByTransactionEntity(TransactionEntity transactionEntity);
}
