package com.peoplestrust.account.external.api.v1.service;

import com.peoples.banking.util.logger.annotation.PerfLogger;
import com.peoplestrust.account.client.domain.model.RetrieveLedgerAccountBalanceByClientResponse;
import com.peoplestrust.account.external.api.v1.config.ClientAccountProperty;
import com.peoplestrust.util.api.common.config.APICommonUtilConstant;
import com.peoplestrust.util.api.common.exception.ResourceAccountNotFoundException;
import com.peoplestrust.util.api.common.util.Messages;
import java.math.BigDecimal;
import java.util.Arrays;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

@Slf4j
@Service
@Component
public class ValidationService {

  @Autowired
  RestTemplate restTemplate;

  @Autowired
  ClientAccountProperty clientAccountProperty;

  /**
   * Adapter to check profile status and client id  from Profile API.
   *
   * @param refId         unique identifier of the profile
   * @param interactionId interaction id to link the transaction API call to the profile API call
   * @return
   * @throws ResourceAccountNotFoundException
   */
  @PerfLogger
  public Boolean isProfileAvailable(String refId, String interactionId, String cognitoClientId) throws ResourceAccountNotFoundException {

    // null check
    if (refId == null) {
      throw new ResourceAccountNotFoundException(Messages.PROFILE_NOT_FOUND);
    }

    // build HTTP headers
    HttpHeaders headers = new HttpHeaders();
    headers.setAccept(Arrays.asList(MediaType.APPLICATION_JSON));
    headers.set(APICommonUtilConstant.HEADER_INTERACTION_ID, interactionId);
    headers.set(APICommonUtilConstant.HEADER_CLIENT_ID, cognitoClientId);

    HttpEntity<String> entity = new HttpEntity<>(headers);

    log.debug("invoking Profile API::Retrieve status and check if client id matches for ref_id = {}, interaction_id = {}", refId, interactionId);

    // returns TRUE if profile is found and status is ENABLED
    ResponseEntity<Boolean> profileStatus = restTemplate
        .exchange(clientAccountProperty.getProfileInternalUrl() + "/validate/" + refId + "/" + cognitoClientId, HttpMethod.GET, entity, Boolean.class);

    // profile API returns FALSE if not found, or if status is inactive or if client id doesn't belong to profile
    log.debug("result = {}", profileStatus);

    return profileStatus.getBody();
  }


  /**
   * Adapter to get account related balances from Transaction API
   *
   * @param refId           Unique ID that identifies the account
   * @param profileRefId    Unique ID that identifies the profile
   * @param interactionId   Unique ID
   * @param overdraftAmount Overdraft amount related to account
   * @return
   */
  public RetrieveLedgerAccountBalanceByClientResponse retrieveBalance(String refId, String profileRefId, String interactionId, BigDecimal overdraftAmount) {

    // build HTTP headers
    HttpHeaders headers = new HttpHeaders();
    headers.setAccept(Arrays.asList(MediaType.APPLICATION_JSON));
    headers.set(APICommonUtilConstant.HEADER_INTERACTION_ID, interactionId);
    headers.set(APICommonUtilConstant.HEADER_PROFILE_ID, profileRefId);
    headers.set(APICommonUtilConstant.OVERDRAFT_AMOUNT, overdraftAmount.toString());

    HttpEntity<String> entity = new HttpEntity<>(headers);

    log.debug("invoking Transaction API::Retrieve balance for ref_id = {}, interaction_id = {}", refId, interactionId);

    ResponseEntity<RetrieveLedgerAccountBalanceByClientResponse> transaction =
        restTemplate.exchange(
            clientAccountProperty.getTransactionInternalUrl() + "/" + refId,
            HttpMethod.GET,
            entity,
            RetrieveLedgerAccountBalanceByClientResponse.class);

    log.debug("result = {}", transaction.getBody());
    return transaction.getBody();
  }
}