package com.peoplestrust.account.external.api.v1.controller;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.peoplestrust.account.client.domain.model.RetrieveLedgerAccountBalanceByClientResponse;
import com.peoplestrust.account.external.api.v1.model.Account;
import com.peoplestrust.account.external.api.v1.model.Options;
import com.peoplestrust.account.external.api.v1.service.ClientAccountService;
import com.peoplestrust.util.api.common.config.APICommonUtilConstant;
import com.peoplestrust.util.api.common.exception.ResourceNotFoundException;
import com.peoplestrust.util.api.common.util.DateUtils;
import java.math.BigDecimal;
import java.time.Instant;
import java.util.Objects;
import java.util.UUID;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;

@Slf4j
public class AccountControllerIT extends DefaultControllerTest {

  private static String profileId = "28dfaa4d-5889-4cbd-bd46-e6201e560b6c";
  String URL = "/v1/ledger/account/external";

  @MockBean
  ClientAccountService accountService;

  HttpHeaders headers;
  private ObjectMapper objectMapper;
  private Account account;

  @BeforeEach
  public void setupBeforeTest() throws Exception {
    super.setup();
    account = createAccount();
    objectMapper = new ObjectMapper();
    objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);

    UUID interactionId = UUID.randomUUID();
    String JWT_TOKEN = "eyJhbGciOiJIUzI1NiJ9."
        + "*******************************************************************************."
        + "ggG7G37dc3f7oXwKOeqTnjFhOqHtSqJX9N-TAZmRxF4";
    String authorizationHeader = "Bearer " + JWT_TOKEN;
    headers = new HttpHeaders();
    headers.add(APICommonUtilConstant.HEADER_AUTHORIZATION, authorizationHeader);
    headers.add(APICommonUtilConstant.HEADER_INTERACTION_ID, interactionId.toString());
    headers.add(APICommonUtilConstant.HEADER_INTERACTION_TIMESTAMP, Instant.now().toString());
    headers.add(APICommonUtilConstant.HEADER_PROFILE_ID, "28dfaa4d-5889-4cbd-bd46-e6201e560b6c");
  }


  @Test
  void getBalanceTest() throws Exception {
    RetrieveLedgerAccountBalanceByClientResponse response = getBalanceResponse();
    when(accountService.retrieveLedgerAccountBalanceForClient(any(), any(), any(),any(),any())).thenReturn(response);
    account = createAccount();
    this.mockMvc
        .perform(
            MockMvcRequestBuilders.get(URL + "/" + account.getRefId() + "/balance")
                .headers(headers)
                .contentType(MediaType.APPLICATION_JSON)
                .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isOk())
        .andReturn();
  }

  private RetrieveLedgerAccountBalanceByClientResponse getBalanceResponse() {
    return RetrieveLedgerAccountBalanceByClientResponse.builder().
        accountBalance(BigDecimal.valueOf(200))
        .availableBalance(BigDecimal.valueOf(1000))
        .effectiveOn(DateUtils.offsetDateTime())
        .fundHoldAmount(BigDecimal.valueOf(200))
        .prefundReserveAmount(BigDecimal.valueOf(400))
        .overdraftAmount(BigDecimal.valueOf(600)).build();
  }

  private Account createAccount() throws Exception {
    UUID uuid = UUID.randomUUID();
    Options options = new Options();
    options.setFundHoldDays(4);
    options.setOverdraftAmount(BigDecimal.valueOf(121.09));

    Account account =
        Account.builder()
            .refId(uuid.toString())
            .status(com.peoplestrust.account.persistence.entity.AccountStatus.INACTIVE)
            .monetaryUnit("CAD")
            .name("TEST-NAME")
            .options(options)
            .description("TEST_DESCRIPTION")
            .createdDateTime(DateUtils.offset())
            .updatedDateTime(DateUtils.offset())
            .profileId(profileId)
            .build();
    return account;
  }

  @Test
  void getBalanceTest_notFound() throws Exception {
    doThrow(new ResourceNotFoundException("Account Not found"))
        .when(accountService)
        .retrieveLedgerAccountBalanceForClient(
            anyString(), anyString(), anyString(), anyString(), anyString());
    MvcResult result =
        this.mockMvc
            .perform(
                MockMvcRequestBuilders.get(URL + "/" + UUID.randomUUID() + "/balance")
                    .headers(headers)
                    .contentType(MediaType.APPLICATION_JSON)
                    .accept(MediaType.APPLICATION_JSON))
            .andExpect(status().isNotFound())
            .andReturn();
    objectMapper = new ObjectMapper();
    JsonNode jsonNode = objectMapper.readTree(Objects.requireNonNull(result.getResponse().getContentAsString()));
    assertEquals("Account Not found", jsonNode.get("error").get("additional_information").asText());
  }

  @Test
  void getBalanceTest_BadRequest() throws Exception {
    RetrieveLedgerAccountBalanceByClientResponse response = getBalanceResponse();
    account = createAccount();
    UUID interactionId = UUID.randomUUID();
    String JWT_TOKEN = "eyJhbGciOiJIUzI1NiJ9."
        + "*******************************************************************************."
        + "ggG7G37dc3f7oXwKOeqTnjFhOqHtSqJX9N-TAZmRxF4";
    String authorizationHeader = "Bearer " + JWT_TOKEN;
    HttpHeaders headers1 = new HttpHeaders();
    headers1.add(APICommonUtilConstant.HEADER_AUTHORIZATION, authorizationHeader);
    headers1.add(APICommonUtilConstant.HEADER_INTERACTION_ID, interactionId.toString());
    headers1.add(APICommonUtilConstant.HEADER_INTERACTION_TIMESTAMP, Instant.now().toString());
    headers1.add(APICommonUtilConstant.HEADER_PROFILE_ID,"aaf672a6-1a9d-4c81-87d7-416e32e7eb98judjd");
    MvcResult result= this.mockMvc
        .perform(
            MockMvcRequestBuilders.get(URL + "/" + UUID.randomUUID() + "/balance")
                .headers(headers1)
                .contentType(MediaType.APPLICATION_JSON)
                .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isBadRequest())
        .andReturn();
  }

  @Override
  public void doCleanUpAfterTest() {
  }
}
