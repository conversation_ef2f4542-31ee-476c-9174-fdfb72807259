<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>

  <groupId>com.peoplestrust</groupId>
  <artifactId>general-ledger</artifactId>
  <version>1.0-SNAPSHOT</version>
  <name>General Ledger</name>
  <packaging>pom</packaging>
  <modules>
    <!-- Parent POMs -->
    <module>ledger-parent</module>
    <module>ledger-parent-api</module>
    <module>ledger-parent-domain</module>
    <module>ledger-parent-persistence</module>

    <!-- Common libraries -->
    <module>common-domain</module>
    <module>common-logger</module>
    <module>common-api</module>

    <!-- Profile API -->
    <module>ledger-profile-domain</module>
    <module>ledger-profile-persistence</module>
    <module>ledger-profile-api</module>

    <!-- (Internal) Profile API -->
    <module>ledger-internal-profile-domain</module>
    <module>ledger-internal-profile-api</module>

    <!-- Account API -->
    <module>ledger-account-domain</module>
    <module>ledger-account-persistence</module>
    <module>ledger-account-api</module>

    <!-- Transaction API -->
    <module>ledger-transaction-domain</module>
    <module>ledger-transaction-persistence</module>
    <module>ledger-transaction-api</module>


    <!-- Health API -->
    <module>ledger-health-domain</module>
    <module>ledger-health-api</module>

    <module>ledger-qa-domain</module>
    <module>qa-util-api</module>
    <module>data-loader-util</module>

    <module>ledger-schedulers-persistence</module>
    <module>ledger-schedulers-api</module>

    <!-- Background jobs -->
    <module>ledger-rollback-scheduler-api</module>

    <module>ledger-account-external-domain</module>
    <module>ledger-account-external-api</module>

    <module>ledger-transaction-async-domain</module>
    <module>ledger-transaction-async-api</module>
    <module>ledger-transaction-async-listener</module>
  </modules>
</project>
