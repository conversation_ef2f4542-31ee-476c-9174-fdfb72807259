package com.peoplestrust.transaction.async.listener.config;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;


@Configuration
@Getter
@Setter
@NoArgsConstructor
public class AsyncTransactionListenerProperty {


  /**
   * Application (API) name.
   */
  @Value("${app.name}")
  private String appName;

  @Value("${app.listenerV2}")
  private boolean asyncListenerV2;

  /**
   * property to define time stamp value
   */
  @Value("${async-transaction.api.timetolive}")
  private int timeToLive;

  /**
   * property to define kafka server address
   */
  @Value("${kafka.bootstrap-servers}")
  private String kafkaBootstrapAddress;

  /**
   * property to define kafka topic name
   */
  @Value("${kafka.topic}")
  private String kafkaTopic;

  /**
   * property to define kafka topic name
   */
  @Value("${kafka.topic.v2}")
  private String kafkaTopicV2;

  /**
   * property to enable or disable SSL
   */
  @Value("${kafka.ssl.enabled}")
  private boolean sslEnabled;

  /**
   * property to define sasl password
   */
  @Value("${kafka.sasl.jaas.config.password}")
  private String saslJaasConfigPassword;

  /**
   * property to define sasl username
   */
  @Value("${kafka.sasl.jaas.config.username}")
  private String saslJaasConfigUsername;

  /**
   * property to define the idle time between polls
   */
  @Value("${kafka.max.poll.interval.idle.ms}")
  private Long kafkaMaxPollIntervalIdleMs;

  /**
   * property to define max amount of time that can pass between polls of the consumer
   */
  @Value("${kafka.max.poll.interval.ms}")
  private String kafkaMaxPollIntervalMs;

  /**
   * property to define maximum number of records that a single call to poll will return
   */
  @Value("${kafka.max.poll.records}")
  private String kafkaMaxPollRecords;

  /**
   * property to define the GL Initiate API URL
   */
  @Value("${transaction.api.initiate.url}")
  private String InitiateTransactionUrl;


  /**
   * property to define the GL Commit API URL
   */
  @Value("${transaction.api.commit.url}")
  private String CommitTransactionUrl;


  /**
   * property to define the GL Rollback API URL
   */
  @Value("${transaction.api.rollback.url}")
  private String RollbackTransactionUrl;


  /**
   * property to define the GL Rollback API URL
   */
  @Value("${transaction.api.reverse.url}")
  private String ReverseTransactionUrl;

  /**
   * property to define topic name for transactions with FAILED status
   */
  @Value("${kafka.dead.letter.topic}")
  public String DeadLetterTopic;

  /**
   * property to define topic name for transactions failed posting to GL APIs
   */
  @Value("${kafka.dead.letter.error.topic}")
  public String DeadLetterErrorTopic;

  /**
   * property to define the interval between two attempts in milliseconds.
   */
  @Value(value = "${kafka.backoff.interval}")
  private Long interval;

  /**
   * property to define the maximum number of attempts in milliseconds.
   */
  @Value(value = "${kafka.backoff.max_failure}")
  private Long maxAttempts;

  /**
   * property to define group ID
   */
  @Value(value = "${kafka.group.id}")
  private String consumerGroupId;

  /**
   *  Property to define connection timeout for validation service
   */
  @Value(value = "${transaction.api.timeout.connection}")
  private int connectionTimeout;

  /**
   * Property to define read timeout for validation service
   */
  @Value(value = "${transaction.api.timeout.read}")
  private int readTimeout;
}
