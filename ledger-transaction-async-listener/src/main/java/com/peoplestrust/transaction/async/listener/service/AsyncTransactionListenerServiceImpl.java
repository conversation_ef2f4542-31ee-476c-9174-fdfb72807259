package com.peoplestrust.transaction.async.listener.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.peoples.banking.util.logger.annotation.PerfLogger;
import com.peoplestrust.transaction.async.domain.model.InitiateLedgerTransactionAsyncRequest;
import com.peoplestrust.transaction.async.listener.config.AsyncTransactionListenerProperty;
import com.peoplestrust.transaction.async.listener.config.ListenerRetryHandler;
import com.peoplestrust.transaction.async.listener.mapper.AsyncTransactionListenerMapper;
import com.peoplestrust.transaction.async.listener.model.Instruction;
import com.peoplestrust.transaction.domain.model.CommitLedgerTransactionResponse;
import com.peoplestrust.transaction.domain.model.InitiateLedgerTransactionResponse;
import com.peoplestrust.transaction.domain.model.InstructionStatus;
import com.peoplestrust.util.api.common.config.APICommonUtilConstant;
import com.peoplestrust.util.api.common.exception.NonRetryableException;
import com.peoplestrust.util.api.common.exception.RetryableException;
import com.peoplestrust.util.api.common.exception.UnlimitedRetryableException;
import java.nio.charset.StandardCharsets;
import java.time.Instant;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.concurrent.CompletableFuture;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.apache.kafka.clients.producer.ProducerRecord;
import org.apache.kafka.common.header.internals.RecordHeaders;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.kafka.support.Acknowledgment;
import org.springframework.kafka.support.SendResult;
import org.springframework.stereotype.Service;
import org.springframework.web.client.HttpClientErrorException;
import org.springframework.web.client.HttpServerErrorException;
import org.springframework.web.client.ResourceAccessException;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.client.UnknownHttpStatusCodeException;

@Service
@Slf4j
@RequiredArgsConstructor
public class AsyncTransactionListenerServiceImpl implements AsyncTransactionListenerService {

  private final ObjectMapper objectMapper;
  private final RestTemplate restTemplate;
  private final AsyncTransactionListenerProperty asyncTransactionListenerProperty;
  private final AsyncTransactionListenerMapper asyncTransactionListenerMapper;
  private final KafkaTemplate kafkaTemplate;
  private final ListenerRetryHandler listenerRetryHandler;

  private int currentRetry = 0;

  @PerfLogger
  @KafkaListener(id = "asyncTransactionListener",
      topics = "#{'${app.listenerV2}' == 'true' ? '${kafka.topic.v2}' : '${kafka.topic}'}",
      groupId = "${kafka.group.id}",
      containerFactory = "kafkaStatefulRetryListenerContainerFactory"
      , concurrency = "${kafka.concurrency.listeners:8}")
  @Override
  public void processKafkaTransactions(ConsumerRecord<String, Instruction> record, Acknowledgment acknowledgment) throws Exception {

    try {
      String key = record.key();
      boolean isAsyncListenerV2 = asyncTransactionListenerProperty.isAsyncListenerV2();
      log.info("Kafka Record offset = {}, latency = {}, key = {} v2={}",
          record.offset(), Instant.now().toEpochMilli() - record.timestamp(), key, isAsyncListenerV2);
      RecordHeaders headers = new RecordHeaders(record.headers().toArray());
      String value = new String(headers.lastHeader(APICommonUtilConstant.HEADER_OPERATION_ID).value(), StandardCharsets.UTF_8);
      String kafkaKey = null;
      if (!isAsyncListenerV2) {
        log.debug("Kafka Consuming key = {}", key);
      } else {
        kafkaKey = new String(headers.lastHeader(APICommonUtilConstant.HEADER_KAFKA_KEY).value(), StandardCharsets.UTF_8);
        log.debug("Kafka Consuming key = {}", kafkaKey);
      }
      if (value.equals(APICommonUtilConstant.INITIATE)) {
        if (!isAsyncListenerV2) {
          initiateTransaction(record, headers, key);
        } else {
          initiateTransaction(record, headers, kafkaKey);
        }
      } else if (value.equals(APICommonUtilConstant.COMMIT)) {
        if (!isAsyncListenerV2) {
          commitTransaction(headers, key);
        } else {
          commitTransaction(headers, kafkaKey);
        }
      } else if (value.equals(APICommonUtilConstant.ROLLBACK)) {
        if (!isAsyncListenerV2) {
          rollBackTransaction(headers, key);
        } else {
          rollBackTransaction(headers, kafkaKey);
        }
      } else if (value.equals(APICommonUtilConstant.REVERSE)) {
        if (!isAsyncListenerV2) {
          reverseTransaction(headers, key);
        } else {
          reverseTransaction(headers, kafkaKey);
        }
      } else {
        throw new Exception(APICommonUtilConstant.INVALID_OPERATION);
      }
      log.info("Kafka Completed key = {}", kafkaKey);
      acknowledgment.acknowledge();
      currentRetry = 0;
    } catch (RetryableException ex) {
      handleLimitedRetry(record, ex, acknowledgment);
    } catch (UnlimitedRetryableException ex) {
      handleUnlimitedRetry(record, ex, acknowledgment);
    } catch (NonRetryableException ex) {
      listenerRetryHandler.errorHandler(record, ex.getMessage(), acknowledgment);
    } catch (Exception ex) {
      throw ex;
    }
  }

  /**
   * Method to perform Initiate operation/postings to GL
   *
   * @param message
   * @param headers
   * @param key
   * @throws Exception
   */
  @PerfLogger
  public void initiateTransaction(ConsumerRecord<String, Instruction> message, RecordHeaders headers, String key)
      throws Exception {

    Instruction instruction = message.value();

    String interactionId = new String(headers.lastHeader(APICommonUtilConstant.HEADER_INTERACTION_ID).value(), StandardCharsets.UTF_8);
    String profileRefId = new String(headers.lastHeader(APICommonUtilConstant.HEADER_PROFILE_ID).value(), StandardCharsets.UTF_8);
    String accountRefId = new String(headers.lastHeader(APICommonUtilConstant.HEADER_ACCOUNT_ID).value(), StandardCharsets.UTF_8);
    String timeStamp = getTimeStamp(APICommonUtilConstant.INITIATE);

    // Construct header values for GL initiate API
    HttpHeaders newHeaders = new HttpHeaders();
    newHeaders.setAccept(Arrays.asList(MediaType.APPLICATION_JSON));
    newHeaders.set(APICommonUtilConstant.HEADER_INTERACTION_ID, interactionId);
    newHeaders.set(APICommonUtilConstant.HEADER_INTERACTION_TIMESTAMP, timeStamp);
    newHeaders.set(APICommonUtilConstant.HEADER_PROFILE_ID, profileRefId);
    newHeaders.set(APICommonUtilConstant.HEADER_ACCOUNT_ID, accountRefId);
    newHeaders.setContentType(MediaType.APPLICATION_JSON);

    InitiateLedgerTransactionAsyncRequest requestPayload = asyncTransactionListenerMapper.fromInstructionToInitiateLedgerTransactionAsyncRequest(instruction);
    HttpEntity<String> request = new HttpEntity<String>(objectMapper.writeValueAsString(requestPayload), newHeaders);

    log.info("Calling GL for key = {}", key);
    try {
      ResponseEntity<InitiateLedgerTransactionResponse> response = restTemplate
          .exchange(asyncTransactionListenerProperty.getInitiateTransactionUrl(), HttpMethod.POST,
              request,
              InitiateLedgerTransactionResponse.class);
      // If successful posting to GL and status was FAILED, Send to Kafka topic
      if (response.getBody().getStatus().equals(InstructionStatus.FAILED)) {

        final String topic = asyncTransactionListenerProperty.getDeadLetterTopic();
        // Constructing producer record
        ProducerRecord<String, Object> producerRecord = new ProducerRecord<>(topic, null, key, response.getBody(), headers);
        log.warn("Moving key = {} to topic = {} Instruction= {}", key, topic, instruction);
        // Publishing producer record to dead letter topic
        CompletableFuture<SendResult<String, Object>> future = kafkaTemplate.send(producerRecord);

        future.whenComplete((result, ex) -> {
          if (ex != null) {
            handleFailure(key, ex);
          } else {
            handleSuccess(key);
          }
        });
      }
    } catch (ResourceAccessException ex) {
      if (ex.getMessage().contains("Read timed out")) {
        throw new NonRetryableException(ex.getMessage());
      } else {
        log.error("Error while calling GL {}", ex.getMessage());
        throw new UnlimitedRetryableException(ex.getMessage());
      }
    } catch (HttpServerErrorException ex) {
      log.error("Error while calling GL {}", ex.getMessage());
      throw new UnlimitedRetryableException(ex.getMessage());
    } catch (HttpClientErrorException ex) {
      log.error("Error while calling GL {}", ex.getMessage());
      throw new NonRetryableException(ex.getMessage());
    } catch (UnknownHttpStatusCodeException ex) {
      log.error("Error while calling GL {}", ex.getMessage());
      throw new NonRetryableException(ex.getMessage());
    } catch (Exception ex) {
      log.error("Error while calling GL {}", ex.getMessage());
      throw ex;
    }
  }

  /**
   * Method to perform Commit operation/postings to GL
   *
   * @param headers
   * @param key
   * @throws Exception
   */
  @PerfLogger
  public void commitTransaction(RecordHeaders headers, String key) throws Exception {

    String interactionId = new String(headers.lastHeader(APICommonUtilConstant.HEADER_INTERACTION_ID).value(), StandardCharsets.UTF_8);
    String profileRefId = new String(headers.lastHeader(APICommonUtilConstant.HEADER_PROFILE_ID).value(), StandardCharsets.UTF_8);
    String accountRefId = new String(headers.lastHeader(APICommonUtilConstant.HEADER_ACCOUNT_ID).value(), StandardCharsets.UTF_8);
    String timeStamp = getTimeStamp(APICommonUtilConstant.COMMIT);
    String instructionRefId = new String(headers.lastHeader(APICommonUtilConstant.HEADER_INSTRUCTION_REF_ID).value(), StandardCharsets.UTF_8);
    log.info("commitTransaction timeStamp: {}", timeStamp);

    // Construct header values for GL commit API
    HttpHeaders newHeaders = new HttpHeaders();
    newHeaders.setAccept(Arrays.asList(MediaType.APPLICATION_JSON));
    newHeaders.set(APICommonUtilConstant.HEADER_INTERACTION_ID, interactionId);
    newHeaders.set(APICommonUtilConstant.HEADER_INTERACTION_TIMESTAMP, timeStamp);
    newHeaders.set(APICommonUtilConstant.HEADER_PROFILE_ID, profileRefId);
    newHeaders.set(APICommonUtilConstant.HEADER_ACCOUNT_ID, accountRefId);
    newHeaders.setContentType(MediaType.APPLICATION_JSON);
    HttpEntity<String> request = new HttpEntity<String>(newHeaders);

    log.info("Calling GL for key = {}", key);
    try {
      ResponseEntity<CommitLedgerTransactionResponse> response = restTemplate
          .exchange(asyncTransactionListenerProperty.getCommitTransactionUrl() + "/" + instructionRefId, HttpMethod.PATCH,
              request,
              CommitLedgerTransactionResponse.class);
    } catch (ResourceAccessException ex) {
      log.error("Error while calling GL {}", ex.getMessage());
      throw new UnlimitedRetryableException(ex.getMessage());
    } catch (HttpServerErrorException ex) {
      log.error("Error while calling GL {}", ex.getMessage());
      throw new UnlimitedRetryableException(ex.getMessage());
    } catch (HttpClientErrorException ex) {
      log.error("Error while calling GL {}", ex.getMessage());
      throw new NonRetryableException(ex.getMessage());
    } catch (UnknownHttpStatusCodeException ex) {
      log.error("Error while calling GL {}", ex.getMessage());
      throw new NonRetryableException(ex.getMessage());
    } catch (Exception ex) {
      log.error("Error while calling GL {}", ex.getMessage());
      throw ex;
    }
  }

  /**
   * Method to perform Rollback operation/postings to GL
   *
   * @param headers
   * @param key
   * @throws Exception
   */
  @PerfLogger
  public void rollBackTransaction(RecordHeaders headers, String key) throws Exception {

    String interactionId = new String(headers.lastHeader(APICommonUtilConstant.HEADER_INTERACTION_ID).value(), StandardCharsets.UTF_8);
    String profileRefId = new String(headers.lastHeader(APICommonUtilConstant.HEADER_PROFILE_ID).value(), StandardCharsets.UTF_8);
    String accountRefId = new String(headers.lastHeader(APICommonUtilConstant.HEADER_ACCOUNT_ID).value(), StandardCharsets.UTF_8);
    String timeStamp = getTimeStamp(APICommonUtilConstant.ROLLBACK);
    String instructionRefId = new String(headers.lastHeader(APICommonUtilConstant.HEADER_INSTRUCTION_REF_ID).value(), StandardCharsets.UTF_8);
    log.info("rollBackTransaction timeStamp:{}", timeStamp);

    // Construct header values for GL rollback API
    HttpHeaders newHeaders = new HttpHeaders();
    newHeaders.setAccept(Arrays.asList(MediaType.APPLICATION_JSON));
    newHeaders.set(APICommonUtilConstant.HEADER_INTERACTION_ID, interactionId);
    newHeaders.set(APICommonUtilConstant.HEADER_INTERACTION_TIMESTAMP, timeStamp);
    newHeaders.set(APICommonUtilConstant.HEADER_PROFILE_ID, profileRefId);
    newHeaders.set(APICommonUtilConstant.HEADER_ACCOUNT_ID, accountRefId);
    newHeaders.setContentType(MediaType.APPLICATION_JSON);
    HttpEntity<String> request = new HttpEntity<String>(newHeaders);

    log.info("Calling GL for key = {}", key);
    try {
      ResponseEntity<HttpStatus> response = restTemplate
          .exchange(asyncTransactionListenerProperty.getRollbackTransactionUrl() + "/" + instructionRefId, HttpMethod.DELETE,
              request,
              HttpStatus.class);
    } catch (ResourceAccessException ex) {
      log.error("Error while calling GL {}", ex.getMessage());
      throw new UnlimitedRetryableException(ex.getMessage());
    } catch (HttpServerErrorException ex) {
      log.error("Error while calling GL {}", ex.getMessage());
      throw new UnlimitedRetryableException(ex.getMessage());
    } catch (HttpClientErrorException ex) {
      log.error("Error while calling GL {}", ex.getMessage());
      throw new NonRetryableException(ex.getMessage());
    } catch (UnknownHttpStatusCodeException ex) {
      log.error("Error while calling GL {}", ex.getMessage());
      throw new NonRetryableException(ex.getMessage());
    } catch (Exception ex) {
      log.error("Error while calling GL {}", ex.getMessage());
      throw ex;
    }
  }

  /**
   * Method to perform Reverse operation/postings to GL
   *
   * @param headers
   * @param key
   * @throws Exception
   */
  @PerfLogger
  public void reverseTransaction(RecordHeaders headers, String key) throws Exception {

    String interactionId = new String(headers.lastHeader(APICommonUtilConstant.HEADER_INTERACTION_ID).value(), StandardCharsets.UTF_8);
    String profileRefId = new String(headers.lastHeader(APICommonUtilConstant.HEADER_PROFILE_ID).value(), StandardCharsets.UTF_8);
    String accountRefId = new String(headers.lastHeader(APICommonUtilConstant.HEADER_ACCOUNT_ID).value(), StandardCharsets.UTF_8);
    String timeStamp = getTimeStamp(APICommonUtilConstant.REVERSE);
    String instructionRefId = new String(headers.lastHeader(APICommonUtilConstant.HEADER_INSTRUCTION_REF_ID).value(), StandardCharsets.UTF_8);
    String transactionRefId = new String(headers.lastHeader(APICommonUtilConstant.HEADER_TRANSACTION_REF_ID).value(), StandardCharsets.UTF_8);
    log.info("reverseTransaction timeStamp:{}", timeStamp);

    // Construct header values for GL reverse API
    HttpHeaders newHeaders = new HttpHeaders();
    newHeaders.setAccept(Arrays.asList(MediaType.APPLICATION_JSON));
    newHeaders.set(APICommonUtilConstant.HEADER_INTERACTION_ID, interactionId);
    newHeaders.set(APICommonUtilConstant.HEADER_INTERACTION_TIMESTAMP, timeStamp);
    newHeaders.set(APICommonUtilConstant.HEADER_PROFILE_ID, profileRefId);
    newHeaders.set(APICommonUtilConstant.HEADER_ACCOUNT_ID, accountRefId);
    newHeaders.setContentType(MediaType.APPLICATION_JSON);
    HttpEntity<String> request = new HttpEntity<String>(newHeaders);

    log.info("Calling GL for key = {}", key);
    try {
      ResponseEntity<HttpStatus> response = restTemplate
          .exchange(asyncTransactionListenerProperty.getReverseTransactionUrl() + "/" + instructionRefId + "/" + transactionRefId, HttpMethod.DELETE,
              request,
              HttpStatus.class);
    } catch (ResourceAccessException ex) {
      log.error("Error while calling GL {}", ex.getMessage());
      throw new UnlimitedRetryableException(ex.getMessage());
    } catch (HttpServerErrorException ex) {
      log.error("Error while calling GL {}", ex.getMessage());
      throw new UnlimitedRetryableException(ex.getMessage());
    } catch (HttpClientErrorException ex) {
      log.error("Error while calling GL {}", ex.getMessage());
      throw new NonRetryableException(ex.getMessage());
    } catch (UnknownHttpStatusCodeException ex) {
      log.error("Error while calling GL {}", ex.getMessage());
      throw new NonRetryableException(ex.getMessage());
    } catch (Exception ex) {
      log.error("Error while calling GL {}", ex.getMessage());
      throw ex;
    }
  }

  /**
   * Utility method to handle the failure when record sent to DLQ
   *
   * @param key
   * @param ex
   */
  private void handleFailure(String key, Throwable ex) {
    log.error("Failed to process key = {}", key);
    try {
      throw ex;
    } catch (Throwable throwable) {
      log.error("Error in OnFailure: {}", throwable.getMessage());
    }
  }

  /**
   * Utility method to handle the success when record sent to DLQ
   *
   * @param key
   */
  private void handleSuccess(String key) {
    log.info("Processed key = {}", key);
  }

  /**
   * Utility method to try limited retries when RetryableException was thrown
   *
   * @param record
   * @param exception
   * @param acknowledgment
   * @throws Exception
   */
  private void handleLimitedRetry(ConsumerRecord<String, Instruction> record, RetryableException exception, Acknowledgment acknowledgment) throws Exception {
    // Maximum number of retries
    long maxRetries = asyncTransactionListenerProperty.getMaxAttempts();

    while (currentRetry < maxRetries) {
      // Introduce Delay between retries
      Thread.sleep(asyncTransactionListenerProperty.getInterval());
      currentRetry++;
      log.info("handleLimitedRetry->Retrying key = {}, retry count = {}", record.key(), currentRetry);
      processKafkaTransactions(record, acknowledgment);
      return;
    }
    currentRetry = 0;
    listenerRetryHandler.errorHandler(record, exception.getMessage(), acknowledgment);
  }

  /**
   * Utility method to try unlimited retries when UnlimitedRetryableException was thrown
   *
   * @param record
   * @param exception
   * @param acknowledgment
   * @throws Exception
   */
  private void handleUnlimitedRetry(ConsumerRecord<String, Instruction> record, UnlimitedRetryableException exception, Acknowledgment acknowledgment)
      throws Exception {

    long startInterval = asyncTransactionListenerProperty.getInterval();

    while (true) {
      if (currentRetry > 0) {
        long retryInterval = startInterval * currentRetry;
        long fixedDelay = 60000;
        try {
          if (retryInterval >= fixedDelay) {
            log.info("handleUnlimitedRetry->Thread going to sleep mode for  = {}", fixedDelay);
            //Introduce fixed delay
            Thread.sleep(fixedDelay);

          } else {
            log.info("handleUnlimitedRetry->Thread going to sleep mode for  = {}", retryInterval);
            //Introduce variable delay
            Thread.sleep(retryInterval);
          }
        } catch (InterruptedException e) {
          //Interrupt the thread when exception occurs
          Thread.currentThread().interrupt();
          break;
        }
      }
      currentRetry++;
      log.info("Retrying key = {}, retry count = {}", record.key(), currentRetry);
      processKafkaTransactions(record, acknowledgment);
      return;
    }
    currentRetry = 0;
  }

  private String getTimeStamp(String callFrom) {
    DateTimeFormatter formatter = DateTimeFormatter.ofPattern(APICommonUtilConstant.DATE_TIME_FORMAT).withZone(ZoneId.of("Z"));
    String timeStamp = formatter.format(Instant.now());
    log.info(callFrom + " GL timeStamp: {}", timeStamp);
    return timeStamp;
  }
}
