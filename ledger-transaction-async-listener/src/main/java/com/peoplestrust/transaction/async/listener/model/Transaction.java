package com.peoplestrust.transaction.async.listener.model;

import java.math.BigDecimal;
import java.time.OffsetDateTime;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

@Data
@Setter
@Getter
@Builder
@ToString
@EqualsAndHashCode
@NoArgsConstructor
@AllArgsConstructor

/**
 * Transaction DTO.
 */
public class Transaction {

  private String transactionRefId;
  private AsyncPaymentCategoryType paymentCategory;
  private AsyncTransactionStatus status;
  private BigDecimal amount;
  private String monetaryUnit;
  private OffsetDateTime acceptanceDateTime;
  private OffsetDateTime dueDateTime;
}

