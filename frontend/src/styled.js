import styled, { createGlobalStyle } from 'styled-components'


export const GlobalStyles = createGlobalStyle`
    * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
        font-size: 12px;
        font-family: -apple-system, Helvetica Neue, Helvetica, Arial, sans-serif;
                        -webkit-font-smoothing: antialiased;
                        -moz-osx-font-smoothing: grayscale;
    }

    ol,ul {
        list-style: none;
    }

    a {
        text-decoration: none;
    }

    .additional-details-accordion{
        margin-top: 20px;
        & .accordion-header{
            background: rgb(207, 226, 255);
        }
        & .MuiAccordionSummary-content{
            margin: 15px 0px !important;
        }
        & .Mui-expanded .MuiButtonBase-root{
            min-height: 48px !important;
            margin-bottom: 10px;
        }
    }

    .additional-details-accordion .static-text {
        display: none;
    }
    .additional-details-accordion .kxiIpd {
        width: 204%;
    }

    .Mui-error{
        margin: 0 !important;
    }
    .modal-title-text{
        font-size: 18px;
    }
`

export const App = styled.div`
    width: 100%;
    height: 100vh;
    display: grid;
    grid-template-areas: 'header header' 'sidebar content' 'footer footer';
    grid-template-rows: auto 1fr auto;
    grid-template-columns: 240px 1fr;

`  

export const Content = styled.div`
    grid-area: content;
    padding: 40px 20px 20px;

    h2 {
        font-size: 20px;
    }
`

export const ContentConatiner = styled.div`
    width: 100%;
    padding: 15px;
    @media (min-width: 1264px) {
        padding: 0px;
        max-width: 1200px;
        margin: auto;
    }
`