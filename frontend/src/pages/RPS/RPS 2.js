import { useState, useEffect } from 'react'
import Table from 'components/Table'
import rpsForm from 'constants/forms/rpsForm'
import rpsColumns from 'constants/columns/rpsColumns'
import useGetRPS from 'hooks/RPS/useGetRPS'
import useRequest from 'hooks/useRequest'
import moment from 'moment'
import { v4 as uuidv4 } from 'uuid'
import { toast } from 'react-toastify'
import * as S from './styled'

const RPS = () => {
    const { data, actions, loading, setLoading } = useRequest()
    const [formData, setData] = useState({})
    const { trigger, data: rpsData } = useGetRPS(formData)

    const onFilter = async (data) => {
        const { start_time, end_time } = data
        const startDate = new Date(start_time)
        const endDate = new Date(end_time)

        const diffTime = Math.abs(endDate - startDate)
        const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))

        const fromDateValid = moment(start_time).isValid()
        const toDateValid = moment(end_time).isValid()

        if (!start_time || !end_time) {
            toast.error('Start/End Date is required')
            return false
        }

        if (endDate < startDate) {
            toast.error('End date must be greater then Start date')
            return false
        } if (diffDays > 3) {
            toast.error('Gap between Start and End dates must be within 3 days')
            return false
        }

        if (fromDateValid && !toDateValid) {
            toast.error('Please Enter Valid End Time')
            return false
        } else if (!fromDateValid && toDateValid) {
            toast.error('Please Enter Valid Start Time')
            return false
        } else if ((start_time !== '' && !fromDateValid) || (end_time !== '' && !toDateValid)) {
            toast.error('Please Enter correct format of date time inputs')
            return false
        } 

        await setData(data)
        trigger()
        setLoading(true)
    }


    const newRpsData = rpsData?.map?.(item => {
        return {
            ...item,
            id: uuidv4()
        }
    })

    useEffect(() => {
        if (rpsData) {
            setLoading(false)
        }
    }, [rpsData])

    return (
        <S.RPS>
            <Table 
                columns={rpsColumns}
                data={newRpsData || []}
                total={rpsData?.total_items || 0}
                limit={10}
                actions={actions}
                loading={loading}
                onFilter={onFilter}
                filters={rpsForm}
                filterForms={{ grid: 2 }}
                heading='RPS'
                hideFooterPagination
                getRowId={(row) => row.id}
                filterButtonDisabled={loading}
                disableSorting
            />
        </S.RPS>
    )
}


export default RPS