import { useState, useEffect } from "react";
import Table from "components/Table";
import TransactionColumns, {
  actionColumns,
} from "constants/columns/transactionSearch";
import useSearchTransaction from "hooks/Transactions/useSearchTransaction";
import TransactionDetails from "modules/popups/TransactionDetails";
import TransactionFilter from "constants/filters/transactions";
import useRequest from "hooks/useRequest";
import moment from "moment-timezone"; // Use moment-timezone for consistency
import _ from "lodash";
import * as S from "./styled";
import * as GS from "../../styled";
import { toast } from "react-toastify";
import MetaData from "modules/popups/MetaData";
import useAuth from "hooks/useAuth";
import { GL_ADMINISTRATION } from "constants/cognitoGroupsTypes";

const Search = () => {
  const [openMetaDataPopup, setOpenMetaDataPopup] = useState(false);
  const [paymentRail, setPaymentRail] = useState("INTERNAL"); // Default to INTERNAL
  const [profile_id, setProfileId] = useState(
    "a83456db-8b15-4109-8319-c366486dfc1e"
  ); // Default profile_id
  const { data, actions, totalNumber, calculateTotal, resetFilter } =
    useRequest();
  const {
    data: transactionService,
    isLoading,
    refetch,
  } = useSearchTransaction(data) || { data: null, isLoading: false };

  const { isAllowed } = useAuth();
  const isAdmin = isAllowed(GL_ADMINISTRATION);

  const [transactionsData, setTransactions] = useState([]);

  const onPaymentRailSelect = (value) => {
    setPaymentRail(value);
  };

  const onFilter = (newFilters) => {
    const { from_date, to_date } = newFilters;

    if (!profile_id) {
      toast.error("Please select Profile ID");
      return;
    }

    if (
      _.isMatch(data.search, { ...newFilters, profile_id }) &&
      data.page === 0
    ) {
      return false;
    }

    // Validate dates if provided
    if (from_date && !moment(from_date, "YYYY-MM-DD:HH:mm", true).isValid()) {
      toast.error("Please enter a valid Start Time (YYYY-MM-DD HH:mm)");
      return;
    }
    if (to_date && !moment(to_date, "YYYY-MM-DD:HH:mm", true).isValid()) {
      toast.error("Please enter a valid End Time (YYYY-MM-DD HH:mm)");
      return;
    }
    if (
      from_date &&
      to_date &&
      moment(from_date, "YYYY-MM-DD:HH:mm").isAfter(
        moment(to_date, "YYYY-MM-DD:HH:mm")
      )
    ) {
      toast.error("From Date must be before To Date");
      return;
    }

    if (!paymentRail) {
      toast.error("Please select Payment Rail");
      return;
    }

    setTransactions([]);
    resetFilter();

    actions?.onFilter({
      ...newFilters,
      profile_id,
      payment_rail: paymentRail,
    });
  };

  const onProfileSelect = (id) => {
    const profileId = typeof id === "object" && id ? id.id : id;
    setProfileId(profileId);
  };

  // Initial search on page load
  useEffect(() => {
    const today = moment().tz("America/New_York").format("YYYY-MM-DD");
    const defaultFilters = {
      from_date: `${today}:01:00`, // 1:00 AM ET today
      to_date: `${today}:06:00`, // 6:00 AM ET today
    };
    onFilter(defaultFilters);
  }, []); // Empty dependency array for page load only

  useEffect(() => {
    if (transactionService?.transactions) {
      setTransactions(transactionService.transactions);
    } else {
      setTransactions([]);
    }

    if (transactionService?.more_records) {
      calculateTotal(true);
    }
  }, [transactionService]);

  const newTransactionData =
    transactionsData?.map?.((t) => ({
      ...t,
      profile_id,
      paymentRail,
    })) || [];

  const [instructionRefId, setInstructionRefId] = useState(null);
  const [transactionRefId, setTransactionRefId] = useState(null);
  const [accountRefId, setAccountRefId] = useState(null);

  const onOpenPopup = (instructionId, transactionId, accountRefId) => {
    setInstructionRefId(instructionId);
    setTransactionRefId(transactionId);
    setAccountRefId(accountRefId);
    setOpenMetaDataPopup(true);
  };

  const onClosePopup = () => {
    setOpenMetaDataPopup(false);
    setInstructionRefId(null);
    setTransactionRefId(null);
    setAccountRefId(null);
  };

  const tansactionAcountColumn = actionColumns(onOpenPopup, isAdmin);

  const searchTransactionColumn =
    paymentRail === "INTERNAL"
      ? [...TransactionColumns, ...tansactionAcountColumn]
      : TransactionColumns;

  return (
    <GS.ContentConatiner data-testid="page-container">
      <S.Search data-testid="page-container">
        <Table
          columns={searchTransactionColumn}
          data={newTransactionData}
          total={totalNumber * data.limit}
          limit={data.limit}
          actions={actions}
          loading={isLoading}
          filters={TransactionFilter({ onProfileSelect, onPaymentRailSelect })}
          onFilter={onFilter}
          filterForms={{ grid: 5 }}
          heading="Search Transaction"
          getRowId={(row) => row.transaction_ref_id}
          infoModal={TransactionDetails}
          paginationMode="server"
          disableSorting
          hideTotalCount
        />
      </S.Search>
      <MetaData
        show={openMetaDataPopup}
        closeModal={onClosePopup}
        instructionRefId={instructionRefId}
        transactionRefId={transactionRefId}
        profileId={profile_id}
        accountId={accountRefId}
      />
    </GS.ContentConatiner>
  );
};

export default Search;
