import { useState, useEffect } from 'react' 
import { useParams } from 'react-router-dom'
import { toast } from 'react-toastify'
import Details from 'components/Details'
import Button from 'components/Button'
import useAuth from 'hooks/useAuth'
import accountsDetails from 'constants/details/accounts'
import CreateAccount from 'modules/popups/CreateAccount'
import ManualAdjustment from 'modules/popups/ManualAdjustment'
import PrefundAdjustment from 'modules/popups/PrefundAdjustment'
import LatestBalance from 'modules/popups/LatestBalance'
import useGetAccountById from 'hooks/Account/useGetAccountById'
import useGetAccountBalance from 'hooks/Account/useGetAccountBalance'
import useGetProfileById from 'hooks/Profile/useGetProfileById'
import useGetInstructions from 'hooks/Account/useGetInstructions'
import instructionsFilter from 'constants/filters/instructions'
import useRequest from 'hooks/useRequest'
import Snapshots from './Snapshots'

import InstructionsColumn from 'constants/columns/instructions'

import { GL_ADMINISTRATION, GL_OPERATIONS } from 'constants/cognitoGroupsTypes'

import Table from 'components/Table'
import * as S from './styled'
import * as _ from 'lodash'

const Account = () => {
    const { isAllowed } = useAuth()
    const { profileId, accountId } = useParams()
    const [show, setShow] = useState(false)
    const [ adjustmentShow, setAdjustmentShow ] = useState(false)
    const [ prefundAdjustmentShow, setPrefundAdjustmentShow ] = useState(false)
    const [ balanceShow, setBalanceShow ] = useState(false)
    const balanceData = useGetAccountBalance(profileId, accountId)

    const profile = useGetProfileById(profileId)
    const data = useGetAccountById(profileId, accountId)

    const { data: instructionData, totalNumber, calculateTotal, actions, resetFilter } = useRequest({
        search: {
            payment_rail: 'ETRANSFER'
        }
    })
    const [instructions, setInstructions] = useState([])
    const { data: instructionsFromServer, isLoading, refetch } = useGetInstructions(instructionData, { profile_id: profileId, account_id: accountId })

    const isAdmin = isAllowed(GL_ADMINISTRATION)
    const isEditAccountAllowed = isAllowed([GL_OPERATIONS, GL_ADMINISTRATION])

    const onFilter = (newFilters) => {
        const { from_date, to_date, instruction_id, payment_rail } = newFilters

        if (_.isMatch(instructionData.search, {...newFilters }) && instructionData.page === 0) {
            return false
        }
        
        setInstructions([])
        resetFilter()

        if (_.isMatch(instructionData.search, {...newFilters })) {
            refetch()
        }

        if ((!from_date || !to_date) && !instruction_id && !payment_rail) {
            toast.error('Choose at lease one of them: From/To Date, Instruction ID or Payment rail')
        } else if(from_date > to_date) {
            toast.error('Please choose valid dates - From date must be less then To Date')
        } else {
            actions?.onFilter({...newFilters })
        }
    }

    useEffect(() => {
        if (instructionsFromServer) {
            setInstructions(instructionsFromServer)
        }

        if (instructionsFromServer?.more_records) {
            calculateTotal(true)
        }
    }, [instructionsFromServer])


    return (
        <S.Account data-testid='page-container'>
            <S.AccountHeader>
                <h2 data-testid='page-title'>
                    Account Details
                </h2>
                {
                    isEditAccountAllowed && (
                        <Button type='primary' onClick={() => setShow(true)} testId='update-button'>
                            Edit Account
                        </Button>
                    )
                }
                <Button type='primary' onClick={() => setBalanceShow(true)} testId='balance-button'>
                    Get Latest Balance
                </Button>
            </S.AccountHeader>
            <S.AccountContent>
                <Details data={data} details={accountsDetails({ crm_id: profile?.crm_id })} />
            </S.AccountContent>
            <Snapshots />
            <S.AccountBalance>
                <p>
                    <b>Current Balance:</b> ${balanceData?.available_balance?.toFixed(2) || 0}
                </p>
                <S.ButtonGroup>
                    {
                        isAdmin && (
                            <>
                                <Button type='primary' onClick={() => setAdjustmentShow(true)} testId='manul-adjustment-button'>
                                    Manual Adjustment
                                </Button>
                                <Button type='primary' onClick={() => setPrefundAdjustmentShow(true)} testId='deposit-funds-button'>
                                    Prefund Adjustment
                                </Button>
                            </>
                        )
                    }
                   
                </S.ButtonGroup>
            </S.AccountBalance>
            <Table 
                columns={InstructionsColumn({ profileId, accountId })}
                data={instructions?.instructions}
                total={totalNumber * instructionData.limit}
                filters={instructionsFilter}
                onFilter={onFilter}
                limit={instructionData?.limit}
                actions={actions}
                loading={isLoading}
                filterForms={{ grid: 5 }}
                heading='Instructions'
                paginationMode='server'
                getRowId={(row) => row.instruction_ref_id}
                hideTotalCount={true}
            />
            {
                isEditAccountAllowed && (
                    <CreateAccount
                        show={show}
                        handleClose={() => setShow(false)}
                        data={data}
                        edit={true}
                        parentId={profileId}
                        testId='update-account-modal'
                    />
                )
            }
            {
                isAdmin && (
                    <>
                        <ManualAdjustment
                            show={adjustmentShow}
                            data={{ profileId, accountId }}
                            handleClose={() => setAdjustmentShow(false)}
                            testId='manual-adjustment-modal'
                        />
                        <PrefundAdjustment
                            show={prefundAdjustmentShow}
                            data={{ profileId, accountId }}
                            handleClose={() => setPrefundAdjustmentShow(false)}
                            testId='prefund-adjustment-modal'
                        />
                    </>
                )
            }
            
            <LatestBalance
                show={balanceShow}
                profileId={profileId}
                accountId={accountId}
                handleClose={() => setBalanceShow(false)}
            />
        </S.Account>
    )
}

export default Account