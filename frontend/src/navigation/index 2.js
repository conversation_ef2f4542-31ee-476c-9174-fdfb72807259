import useAuth from 'hooks/useAuth'
import React, { Suspense } from 'react'
import { Route, Routes } from 'react-router-dom'
import getRoutes from './routes'

const RoutesMain = () => {
  const { isSignedIn } = useAuth()
  const routes = getRoutes(isSignedIn)

  return (
    <Suspense fallback={<div>Loading...</div>}>
      <Routes data-test='routes'>
        {routes.map((route, key) => {
          const { Component } = route
          return (
            <Route exact={route.exact} path={route.path} element={<Component />} key={key} />
          )
        })}
      </Routes>
    </Suspense>
  )
}


export default RoutesMain