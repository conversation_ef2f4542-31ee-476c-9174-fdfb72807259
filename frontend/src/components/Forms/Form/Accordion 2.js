import React from 'react'
import Accordion from '@mui/material/Accordion'
import AccordionSummary from '@mui/material/AccordionSummary'
import AccordionDetails from '@mui/material/AccordionDetails'
import Typography from '@mui/material/Typography'
import ExpandMoreIcon from '@mui/icons-material/ExpandMore'
import _ from 'lodash'
import * as S from './styled'

const FormAccordion = ({ data = [], touched, errors, handleChange, setValues, values, InputComponent, blackList }) => {

    if (!data.length) {
        return null
    }


    return (
        <S.Accordion>
            {
                data.map((acc, key) => (
                    <Accordion key={`card-${key}`} data-testid='accordion'>
                        <AccordionSummary
                            expandIcon={<ExpandMoreIcon />}
                            aria-controls={acc.name}
                        >
                            <Typography>{acc?.label}</Typography>
                        </AccordionSummary>
                        <AccordionDetails>
                            {
                                acc?.children?.map((child, childKey) => {
                                    const name = `${acc.name}.${child.name}`
                                    return (
                                        <InputComponent 
                                            {..._.omit(child, [...blackList, 'name'])}
                                            touched={_.get(touched, name)}
                                            error={_.get(errors, name)}
                                            value={_.get(values, name)}
                                            name={name}
                                            onChange={handleChange}
                                            setValues={setValues}
                                            values={values}
                                            key={`child-input-${childKey}`}
                                            data-testid='accordion-input'
                                        />
                                    )
                                })
                            }
                        </AccordionDetails>
                    </Accordion>
                ))
            }
        </S.Accordion>
    )

}

export default FormAccordion