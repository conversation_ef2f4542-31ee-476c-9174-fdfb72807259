import * as S from './styled'

const types = {
    default: <PERSON><PERSON>,
    primary: <PERSON><PERSON>,
    secondary: S.ButtonSecondary
}

const Button = (props) => {
    const ButtonComponent = types[props.type] || types.default
    const testId = props.testId || `button-${props.type || 'default'}`
    return (
        <ButtonComponent {...props} data-testid={testId} />
    )
}

export default Button