import styled from "styled-components"

export const Details = styled.div`
    width: 100%;
    max-height: 580px;
    display: flex;
    flex-direction: column;
    gap: 15px;
    flex-wrap: wrap;
`

export const DetailsItem = styled.div`
    display: flex;
    gap: 10px;

    b, p {
        font-size: 14px;
    }

`

export const DetailsGroup = styled.div`

    display: flex;
    flex-direction: column;
    gap: 12px;

    h3 {
        margin-bottom: 5px;
        font-weight: bold;
        font-size: 16px;
    }

    b {
        font-weight: 400;
    }

`