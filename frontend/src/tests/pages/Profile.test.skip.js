// Profile.test.skip.js
import React from 'react';
import { render, screen, act, fireEvent } from '@testing-library/react';
import Profile from 'pages/Profile';
import { QueryClientProvider } from 'react-query';
import { mswServer } from 'mocks/mswServer';
import { fetch_profile_by_id } from 'mocks/mswHandlers/profilesHandler';
import queryClient from 'services/client/queryClient';

// Mock react-router to force useParams() to return id=1
jest.mock('react-router-dom', () => ({
    ...jest.requireActual('react-router-dom'),
    useParams: () => ({ id: 1 }),
    useLocation: () => ({ search: '' }),
}));

// Simple helper to render <Profile> with QueryClientProvider
const setUp = (props) => {
    render(
        <QueryClientProvider client={queryClient}>
            <Profile {...props} />
        </QueryClientProvider>
    );
};

describe('Profiles page deep render', () => {
    beforeAll(() => {
        // Use the MSW handler that returns the mock profile data
        mswServer.use(fetch_profile_by_id);
    });

    it('Should render page correctly', async () => {
        // Render the component
        await act(async () => {
            setUp({});
        });
        // Now wait for the container to appear
        const container = await screen.findByTestId('page-container');
        expect(container).toBeInTheDocument();
    });

    it('Should render page title correctly', async () => {
        await act(async () => {
            setUp({});
        });
        // Wait for the page title to appear
        const title = await screen.findByTestId('page-title');
        // Now it should have the fetched data text, e.g. 'Test Account'
        expect(title).toHaveTextContent('Test Account');
    });

    it('Should render update button', async () => {
        await act(async () => {
            setUp({});
        });
        const button = await screen.findByTestId('update-button');
        expect(button).toBeInTheDocument();
        expect(button).toHaveTextContent('Update Profile');
    });

    it('Update button trigger should render modal component', async () => {
        await act(async () => {
            setUp({});
        });
        // Wait for the update button
        const button = await screen.findByTestId('update-button');
        fireEvent.click(button);

        // After clicking, the update profile modal should appear
        const modal = await screen.findByTestId('update-profile-modal');
        expect(modal).toBeInTheDocument();
    });

    describe('Table component', () => {
        it('Should render page table correctly', async () => {
            await act(async () => {
                setUp({});
            });
            const table = await screen.findByTestId('table-container');
            expect(table).toBeInTheDocument();
        });

        it('Should render create button with table', async () => {
            await act(async () => {
                setUp({});
            });
            const button = await screen.findByTestId('create-modal-button');
            expect(button).toBeInTheDocument();
            expect(button).toHaveTextContent('Create Account');
        });

        it('Create button trigger should render modal component', async () => {
            await act(async () => {
                setUp({});
            });
            const button = await screen.findByTestId('create-modal-button');
            fireEvent.click(button);

            const modal = await screen.findByTestId('create-account-modal');
            expect(modal).toBeInTheDocument();
        });
    });

    describe('Details component', () => {
        it('Should render component correctly', async () => {
            await act(async () => {
                setUp({});
            });
            const details = await screen.findByTestId('details');
            expect(details).toBeInTheDocument();
        });
    });
});
