import Form from 'components/Forms/Form/Form'
import Accordion from 'components/Forms/Form/Accordion' 
import { render } from '@testing-library/react'
import Input from 'components/Forms/Input'
import findByTestId from 'utils/findByTestId'

const formData = [
    {
        name: 'name',
        label: 'Name'
    },
    {
        name: 'description',
        label: 'Description'
    }
]

const formDataWithChildren = [
    {
        name: 'name',
        label: 'Name',
        children: [
            {
                name: 'child',
                label: 'Child'
            }
        ]
    },
]

const setUp = (props) => render(
    <Form {...props} />
)

const setUpAccordion = (props) => render(
    <Accordion {...props} />
)

describe.skip('Form Component', () => {
    it('Should render without props', async () => {
        setUp({})
        const component = await findByTestId('custom-form')
        expect(component).toBeInTheDocument()
    })

    it ('Should render with formData', async () => {
        setUp({
            formData
        })
        const inputs = await findByTestId('default-input', 'all')

        expect(inputs.length).toEqual(formData.length)

    })


    it ('Should render custom Input', async () => {

        const CustomInput = () => <input data-testid='custom-input' />
        setUp({
            formData,
            customInput: CustomInput
        })

        const inputs = await findByTestId('custom-input', 'all')

        expect(inputs.length).toEqual(2)

    })

    it ('Should render form button', async () => {
        setUp({
            formData
        })

        const button = await findByTestId('form-button')

        expect(button).toBeInTheDocument()
    })

    it ('Should not render form button', async () => {
        setUp({
            formData,
            noButton: true
        })

        const button = await findByTestId('form-button', 'query')

        expect(button).not.toBeInTheDocument()
    })
})


describe('Accordion Component for Forms', () => {
    it(`Should render with data props`, async () => {
        setUpAccordion({
            data: formDataWithChildren,
            blackList: [],
            touched: {},
            setValues: jest.fn(),
            InputComponent: Input
        })

        const accordion = await findByTestId('accordion')
        const accordionInputs = await findByTestId('default-input')

        expect(accordion).toBeInTheDocument()
        expect(accordionInputs).toBeInTheDocument()

    })

})