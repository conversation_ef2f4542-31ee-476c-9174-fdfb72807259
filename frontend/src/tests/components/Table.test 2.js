import Table from 'components/Table'
import TableHeader from 'components/Table/TableHeader'
import { render } from '@testing-library/react'
import findByTestId from 'utils/findByTestId'

jest.mock('react-router-dom', () => ({
    ...jest.requireActual('react-router-dom'),
    useParams: () => ({
      id: 9
    }),
    useLocation: () => ({
        search: ''
    })
}))

const setUp = (props, Component = Table) => {
    render(
        <Component {...props} />
    )
}

const columns = [
    {
        headerName: 'ID',
        field: 'id'
    },
    {
        headerName: 'Test Column',
        field: 'testColumn'
    }
]

const data = [
    {
        id: 1,
        testColumn: 'yeas'
    },
    {
        id: 2,
        testColumn: 'yeas'
    }
]

describe('Table Component', () => {

    describe("With no props", () => {
        beforeEach(() => {
            setUp({
                hideSearch: true,
                createModal: null,
                hidePagination: true,
                noEdit: true
            })
        })

        it('Should render without props', () => {
            const component = findByTestId('table-container')
            expect(component).toBeInTheDocument()
        })

        it('Should not render info modal', () => {
            const component = findByTestId('info-modal', 'query')
            expect(component).not.toBeInTheDocument()
        })
        
        it('Should not render create modal', () => {
            const wrapper = findByTestId('create-modal', 'query')
            expect(wrapper).not.toBeInTheDocument()
        })
        
    })

    describe('With props', () => {
        beforeEach(() => {
            const props = {
                data,
                columns,
                hideSearch: false,
                createModal: (props) => <div data-testid={props['data-testid']} />,
                infoModal: (props) => <div data-testid={props['data-testid']} />,
                hidePagination: false,
                total: data.length,
                onPageClick: jest.fn(),
                onDelete: jest.fn(),
                onEnable: jest.fn(),
                onDisable: jest.fn()
            }
            setUp(props)
        })

        it('Should render info modal', () => {
            const wrapper = findByTestId('info-modal')
            expect(wrapper).toBeInTheDocument()
        })
        
        it('Should render create modal', () => {
            const wrapper = findByTestId('create-modal')
            expect(wrapper).toBeInTheDocument()
        })

    })

})


describe('TableHeader Component', () => {

    describe('With props', () => {
        beforeEach(() => {
            const props = {
                data,
                columns,
                hideSearch: false,
                createModal: (props) => <div data-testid={props['data-testid']} />,
                infoModal: (props) => <div data-testid={props['data-testid']} />,
                onDelete: jest.fn(),
                onEnable: jest.fn(),
                onDisable: jest.fn(),
                filters: [
                    {
                        name: 'id',
                        label: "ID",
                    },
                    {
                        name: 'firstName',
                        label: "First Name",
                    },
                    {
                        name: 'lastName',
                        label: "Last Name",
                    }
                ]
            }
            setUp(props, TableHeader)
        })
        
        it('Should render table header', () => {
            const wrapper = findByTestId('table-header')
            expect(wrapper).toBeInTheDocument()
        })
       
    })

})