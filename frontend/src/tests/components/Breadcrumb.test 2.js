import { render } from '@testing-library/react'
import Breadcrumb from 'components/Breadcrumb'
import findByTestId from 'utils/findByTestId'
import { routesForTesting } from 'navigation/routes'
import { MemoryRouter} from 'react-router-dom'

const setUp = (props, entries = []) => {
    render(
        <MemoryRouter initialEntries={entries}>
            <Breadcrumb {...props} routes={routesForTesting(false)} />
        </MemoryRouter>
    )
}


describe('Breadcrumb component', () => {

    it ('should be null with empty route', () => {
        setUp({}, ['/'])
        const breadcrumb = findByTestId('breadcrumb', 'query')
        expect(breadcrumb).not.toBeInTheDocument()
    })

    it('should render with one simple route /profiles', () => {
        setUp({}, ['/profiles'])
        const breadcrumb = findByTestId('breadcrumb')
        const items = findByTestId('breadcrumb-item', 'all')
        expect(breadcrumb).toBeInTheDocument()
        expect(items).toHaveLength(1)
    })

    it ('should render route name correctly on simple route /profiles', () => {
        setUp({}, ['/profiles'])
        const items = findByTestId('breadcrumb-item', 'all')
        expect(items[0]).toHaveTextContent('Test Profiles')
    })

    it('should render with child route /profiles/1377', () => {
        setUp({}, ['/profiles/1377'])
        const items = findByTestId('breadcrumb-item', 'all')
        expect(items).toHaveLength(2)
    })

    it ('should render route name correctly on child route /profiles/1377', () => {
        setUp({}, ['/profiles/1377'])
        const items = findByTestId('breadcrumb-item', 'all')
        expect(items[0]).toHaveTextContent('Test Profiles')
        expect(items[1]).toHaveTextContent('Test Profile Details')
    })

})