import React from 'react'
import Input from 'components/Forms/Input'
import { render } from '@testing-library/react'
import findByTestId from 'utils/findByTestId'

const setUp = (props) => render(
    <Input {...props} />
)

const typeChecking = (type) => {
    it(`Should render input with type ${type}`, async () => {
        setUp({
            type
        })

        const component = await findByTestId(`${type}-input`)

        expect(component).toBeInTheDocument()
    })
}

describe('Should Render components with type props', () => {
    typeChecking('default')
    typeChecking('search')
    typeChecking('date')
    typeChecking('select')
    typeChecking('yesorno')
})