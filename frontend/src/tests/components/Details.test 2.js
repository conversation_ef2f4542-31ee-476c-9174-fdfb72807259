import { render, fireEvent } from '@testing-library/react'
import Details from 'components/Details'
import findByTestId from 'utils/findByTestId'



const setUp = (props) => {
    render(
        <Details {...props} />
    )
}

describe('Details component', () => {

    it('should render correctly', () => {
        setUp()
        const details = findByTestId('details')
        expect(details).toBeInTheDocument()
    })

    describe('Render without childs', () => {
        const data = {
            testName: 'Test 123',
            testNumber: 'Test Number 123',
            testId: 23
        }
        
        const details = [
            {
                title: 'Test Name',
                key: 'testName'
            },
            {
                title: 'Test Number',
                key: 'testNumber'
            },
            {
                title: 'Test ID',
                key: 'testId'
            },
        ]

        beforeEach(() => {
            setUp({ data, details })
        })

        it ('Should render component correctly', () => {
            const details = findByTestId('details')
            expect(details).toBeInTheDocument()
        })

        it('Should render details item', () => {
            const detailItems = findByTestId('details-item', 'all')
            expect(detailItems).toHaveLength(details.length)
        })

        it('Should render details items with correct text', () => {
            const detailItems = findByTestId('details-item', 'all')
            
            detailItems.forEach((item, i) => {
                const detail = details[i]
                
                expect(item).toHaveTextContent(`${detail.title}:${data[detail.key]}`)
            })
        })
    })

    describe('Render with childs', () => {
        const data = {
            testName: 'Test 123',
            info: {
                testEmail: '<EMAIL>',
                phone: '556677'
            }
        }
        
        const details = [
            {
                title: 'Test Name',
                key: 'testName'
            },
            {
                title: 'Test Info',
                key: 'info',
                children: [
                    {
                        title: 'Test Email',
                        key: 'testEmail'
                    },
                    {
                        title: 'Test Phone',
                        key: 'phone'
                    }
                ]
            },
            
        ]

        beforeEach(() => {
            setUp({ data, details })
        })

        it ('Should render component correctly', () => {
            const details = findByTestId('details')
            expect(details).toBeInTheDocument()
        })

        it('Should render details item', () => {
            const detailItems = findByTestId('details-item', 'all')
            expect(detailItems).toHaveLength(details.filter(d => !d.children).length)
        })

        it('Should render details child conntainer', () => {
            const childContainer = findByTestId('details-group')
            
            expect(childContainer).toBeInTheDocument()
        })

        it('Should render details childs', () => {
            const detailChilds = findByTestId('details-child-item', 'all')
            const detailGroupTitles = findByTestId('details-group-title', 'all')
            const onlyDetailsWithChildrens = details.filter(d => d.children)
            const onlyChilds = onlyDetailsWithChildrens.reduce((prev, next) => {
                return [
                  ...prev,
                  ...next.children
                ]
            }, [])
            
            detailGroupTitles.forEach((item, i) => {
                expect(item).toHaveTextContent(onlyDetailsWithChildrens[i].title)
            })
    
            expect(detailChilds).toHaveLength(onlyChilds.length)
        })
    })

})