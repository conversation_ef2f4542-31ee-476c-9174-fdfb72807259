import { useCallback, useEffect } from "react";
import MetaData from "./MetaData";
import useCreateUpdateMetadata from "hooks/Transactions/useCreateUpdateMetadata";
import useGetMetadata from "hooks/Transactions/useGetMetadata";
import CreateMetaDataForm from "constants/forms/createMetaData";
import { toast } from "react-toastify";
import ModalProvider from "contexts/ModalProvider";
import { createSchema, updateSchema } from "./schema";

const CreateMetaDataContainer = (props) => {
  const {
    profileId,
    accountId,
    instructionRefId,
    transactionRefId,
    show,
    closeModal,
  } = props;

  const { data: metaData, refetch } = useGetMetadata(
    profileId,
    accountId,
    instructionRefId,
    transactionRefId
  );

  useEffect(() => {
    if (show) {
      refetch();
    }
  }, [show, refetch]);

  const edit = !!metaData;

  const onSuccess = () => {
    toast.success("Additional Details saved successfully");
    closeModal();
  };

  const onError = (error) => {
    console.error("Error updating Additional Details:", error);
  };

  const mutation = useCreateUpdateMetadata(onSuccess, onError);

  const handleSubmit = useCallback(
    (values) => {
      const { ticket_id, ticket_type, notes } = values || {};

      const payload = {
        ...(ticket_id && { ticket_id }),
        ...(ticket_type && { ticket_type }),
        ...(notes ? { notes } : {}),
      };

      const variables = {
        profileId,
        accountId,
        instructionRefId,
        transactionRefId,
        payload,
      };

      const hasOneTicketField =
        (!!ticket_id && !ticket_type) || (!ticket_id && !!ticket_type);

      if (hasOneTicketField) {
        toast.error(
          "Both Ticket ID and Ticket Type are required when providing ticket information."
        );
        return false;
      } else if (!ticket_id && !ticket_type && !notes) {
        toast.error(
          "Please provide at least one of: Ticket ID, Ticket Type, or Notes."
        );
        return false;
      }

      if (ticket_id && ticket_id.length > 25) {
        toast.error("Ticket ID must be at most 25 characters long.");
        return false;
      }

      if (notes && (notes.length < 5 || notes.length > 256)) {
        toast.error("Notes must be between 5 and 256 characters long.");
        return false;
      }

      mutation.mutate(variables);
    },
    [profileId, accountId, instructionRefId, transactionRefId, mutation]
  );

  return (
    <ModalProvider
      data={metaData}
      edit={edit}
      schema={edit ? updateSchema : createSchema}
      handleClose={closeModal}
      fields={CreateMetaDataForm}
      show={show}
      handleSubmit={handleSubmit}
    >
      <MetaData
        {...props}
        edit={edit}
        transactionId={transactionRefId}
        mutationData={mutation.data}
        closeModal={closeModal} // Explicitly pass closeModal
        data-testid="modal-component"
      />
    </ModalProvider>
  );
};

export default CreateMetaDataContainer;
