import Form from 'components/Forms/Form'
import manualAdjustmentForm from 'constants/forms/manualAdjustment'
import createMetaDataForm from 'constants/forms/createMetaData'
import { useModal } from 'contexts/ModalProvider'
import * as S from '../styled'
import { 
    Accordion,
    AccordionDetails,
    AccordionSummary,
    Typography
} from '@mui/material';
import ArrowDownwardIcon from '@mui/icons-material/ArrowDownward';
import { useEffect } from 'react'

const ManualAdjustment = (props) => {
  const { show, closeModal, formik } = useModal();

  const handleClose = () => {
    closeModal()
    formik.resetForm();
  };

     useEffect(() => {
        props.setValidation(!formik.isValid || !formik.dirty)
    }, [formik.isValid, formik.dirty, props]);

    return (
        <S.Modal
            show={show}
            handleClose={handleClose}
            onCreate={formik.handleSubmit}
            title='Manual Adjustment'
            createTxt='Save Changes'
            testId={props.testId || 'manual-adjustment-modal'}
            saveDisabled={props.saveDisabled}
        >
            <Form
                formData={manualAdjustmentForm}
                formik={formik}
                data-testid='form'
                noButton
            />
             <div className='additional-details-accordion'>
                <Accordion>
                    <AccordionSummary
                    expandIcon={<ArrowDownwardIcon />}
                    aria-controls="panel1-content"
                    className='accordion-header'
                    id="panel1-header"
                    >
                    <Typography>Additional Details</Typography>
                    </AccordionSummary>
                    <AccordionDetails>
                    <Form
                        formData={createMetaDataForm}
                        formik={formik}
                        data-testid='form'
                        noButton
                    />
                    </AccordionDetails>
                </Accordion>
                </div>
            
        </S.Modal>
    )
}

export default ManualAdjustment;
