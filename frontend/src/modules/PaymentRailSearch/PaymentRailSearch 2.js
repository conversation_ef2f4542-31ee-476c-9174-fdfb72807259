import React, { useState } from 'react'
import Autocomplete from '@mui/material/Autocomplete'
import Input from 'components/Forms/Input'
import RequiredAsterisk from 'components/designElements/RequiredAsterisk.js'
import * as S from './styled'
import _ from 'lodash'

const paymentRailOptions = [
    {
        name: 'EFT',
        value: 'EFT'
    },
    {
        name: 'ETRANSFER',
        value: 'ETRANSFER'
    },
    {
        name: 'WIRES',
        value: 'WIRES'
    },
    {
        name: 'VISA',
        value: 'VISA'
    },
    {
        name: 'INTERNAL',
        value: 'INTERNAL'
    }
];

const PaymentRailSearch = ({ onPaymentRailSelect }) => {
    const [open, setOpen] = useState(false)

    return (
        <S.PaymentRailSearch className='payment-rail-search'>
            <Autocomplete
                id="payment-rail-autocomplete"
                options={paymentRailOptions}
                getOptionLabel={(option) => option.name}
                sx={{ width: 300 }}
                open={open}
                onOpen={() => {
                    setOpen(true)
                }}
                onClose={() => {
                    setOpen(false)
                }}
                onChange={(event, newValue) => onPaymentRailSelect(newValue?.value)}
                renderInput={(params) => (
                    <Input
                        type='text'
                        placeholder="Select Payment Rail"
                        {...params}
                        InputProps={{
                            ...params.InputProps,
                            endAdornment: (
                                <>
                                {params.InputProps.endAdornment}
                                 <RequiredAsterisk left = '141px' />
                                </>
                            )
                        }}
                    />
                )}
            />
        </S.PaymentRailSearch>
    )
}
export default PaymentRailSearch
