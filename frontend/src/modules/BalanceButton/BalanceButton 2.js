import { useState } from 'react'
import { useParams } from 'react-router-dom'
import useGetAccountBalance from 'hooks/Account/useGetAccountBalance'
import amountFormat from "utils/amountFormat"
import * as S from './styled'
import _ from 'lodash'

const BalanceButton = ({ id }) => {
    const [active, setActive] = useState(false)
    const { profileId } = useParams()
    const data = useGetAccountBalance(profileId, id, active)

    const onClick = _.debounce(() => {
        setActive(true)
    }, 200)

    return (
        <S.BalanceButton type='primary' onClick={onClick}>
            <span>Get Balance</span>
            {
                data?.available_balance && (
                    <span className='balance'>
                        {
                            amountFormat(data?.available_balance)
                        }
                    </span>
                )
            }
        </S.BalanceButton>
        
    )
}

export default BalanceButton