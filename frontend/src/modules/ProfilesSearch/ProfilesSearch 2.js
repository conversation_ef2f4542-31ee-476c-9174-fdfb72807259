import React, { useState, useEffect } from 'react'
import Input from 'components/Forms/Input'
import Autocomplete from '@mui/material/Autocomplete'
import useSearchProfiles from 'hooks/Profile/useSearchProfile'
import RequiredAsterisk from 'components/designElements/RequiredAsterisk.js'
import _ from 'lodash'
import * as S from './styled'


const generateOptions = data => data.map(item => ({ label: item?.legal_name, id: item?.ref_id }))

const ProfilesSearch = ({ onProfileSelect, placeholder }) => {
    const [value, setValue] = useState('')
    const [open, setOpen] = useState(false)

    const { trigger, data} = useSearchProfiles(value)

    const options = data?.profiles ? generateOptions(data.profiles) : []

    const onChange = _.debounce((e) => {
        setValue(e.target.value)
    }, 300)


    useEffect(() => {
        if (value) {
            trigger()
        }
    }, [value])

    return (
        <S.ProfilesSearch className='profiles-search'>
            <Autocomplete
                id="asynchronous-demo"
                sx={{ width: 300 }}
                open={open}
                onOpen={() => {
                    setOpen(true)
                }}
                onClose={() => {
                    setOpen(false)
                }}
                onChange={(e, value) => onProfileSelect?.(value?.id) }
                getOptionLabel={(option) => option.label}
                options={options}
                renderInput={(params) => (
                    <Input
                        type="text"
                        placeholder="Select Profile. (type min 3 letter)"
                        {...params}
                        onChange={onChange}

                        InputProps={{
                            ...params.InputProps,
                            endAdornment: (
                                <>
                                {params.InputProps.endAdornment}
                                  <RequiredAsterisk left = '209px' />
                                </>
                            )
                        }}
                    />
                )}
            />
        </S.ProfilesSearch>
    )
}


export default ProfilesSearch