export default [
  {
    type: "statictext", // Or "label", depending on your form component
    label: "Transaction ID",
    name: "transactionRefId", // Optional, but useful for keys
  },
  {
    name: "ticket_id",
    label: "Ticket ID",
    maxLength: 25,
  },
  {
    label: "Ticket Type",
    name: "ticket_type",
    type: "select",
    options: [
      {
        name: "JIR<PERSON>",
        value: "JIRA",
      },
      {
        name: "SERVICE_NOW",
        value: "SERVICE_NOW",
      },
    ],
  },
  {
    name: "notes",
    label: "Notes",
    type: "textarea",
    rows: 2,
    maxLength: 256,
  },
];
